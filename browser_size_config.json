{"browserSizeOptions": {"description": "<PERSON><PERSON><PERSON> hình kích thước trình duy<PERSON>t - Thu nhỏ mà vẫn giữ full giao diện", "minisizeChrome": true, "chromeScaleFactor": 0.3, "chromeZoomLevel": 1.0, "viewportScale": 0.6, "sizePresets": {"tiny": {"description": "Rất nhỏ như trong hình - compact tối đa", "windowSize": [500, 300], "zoom": 0.25, "scale": 0.25, "format": "compact"}, "mini": {"description": "<PERSON>hu nhỏ tối đa - dạng ngang (khuyến nghị)", "windowSize": [600, 350], "zoom": 0.3, "scale": 0.3, "format": "compact"}, "small": {"description": "<PERSON>hu nhỏ vừa phải - dạng ngang", "windowSize": [700, 400], "zoom": 0.35, "scale": 0.35, "format": "compact"}, "normal": {"description": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> bình thường - dạng ngang", "windowSize": [800, 500], "zoom": 0.5, "scale": 0.5, "format": "landscape"}}, "advancedOptions": {"description": "<PERSON><PERSON><PERSON> chọn nâng cao để tối ưu hiệu suất", "disableImages": false, "disableCSS": false, "disableJavaScript": false, "enableGPUAcceleration": true, "reducedMotion": true}, "multiWindowLayout": {"description": "Bố trí nhiều cửa sổ trình du<PERSON>t", "enabled": true, "columns": 3, "rows": 2, "windowSpacing": 5, "autoArrange": true}}, "usage": {"description": "Hướng dẫn sử dụng", "steps": ["1. <PERSON><PERSON><PERSON> 'minisizeChrome': true trong settings.json", "2. <PERSON><PERSON><PERSON><PERSON> chỉnh 'chromeScaleFactor' từ 0.5 đến 1.0", "3. <PERSON><PERSON> 'chromeZoomLevel' để zoom giao di<PERSON>n", "4. S<PERSON> dụng 'viewportScale' để điều chỉnh viewport", "5. <PERSON><PERSON><PERSON> adjustBrowserSize('mini'|'small'|'normal'|'large') trong runtime"], "tips": ["- Scale factor 0.75 (75%) là tối ưu nhất", "- Zoom level 0.75 giữ text đ<PERSON><PERSON> đ<PERSON>", "- Viewport scale 0.8 tương thích mobile", "- Sử dụng 'small' preset cho đa luồng"]}}