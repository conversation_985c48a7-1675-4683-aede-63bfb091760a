{"browserSizeOptions": {"description": "<PERSON><PERSON><PERSON> hình kích thước trình duy<PERSON>t - Thu nhỏ mà vẫn giữ full giao diện", "minisizeChrome": true, "chromeScaleFactor": 0.75, "chromeZoomLevel": 0.75, "viewportScale": 0.8, "sizePresets": {"mini": {"description": "<PERSON>hu nhỏ tối đa nhưng vẫn đọc được", "windowSize": [400, 600], "zoom": 0.6, "scale": 0.6}, "small": {"description": "Thu nhỏ vừa phải - khuyến nghị", "windowSize": [600, 800], "zoom": 0.75, "scale": 0.75}, "normal": {"description": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> bình thư<PERSON>", "windowSize": [800, 1000], "zoom": 1.0, "scale": 1.0}, "large": {"description": "<PERSON><PERSON><PERSON> lớn", "windowSize": [1000, 1200], "zoom": 1.2, "scale": 1.2}}, "advancedOptions": {"description": "<PERSON><PERSON><PERSON> chọn nâng cao để tối ưu hiệu suất", "disableImages": false, "disableCSS": false, "disableJavaScript": false, "enableGPUAcceleration": true, "reducedMotion": true}, "multiWindowLayout": {"description": "Bố trí nhiều cửa sổ trình du<PERSON>t", "enabled": true, "columns": 3, "rows": 2, "windowSpacing": 5, "autoArrange": true}}, "usage": {"description": "Hướng dẫn sử dụng", "steps": ["1. <PERSON><PERSON><PERSON> 'minisizeChrome': true trong settings.json", "2. <PERSON><PERSON><PERSON><PERSON> chỉnh 'chromeScaleFactor' từ 0.5 đến 1.0", "3. <PERSON><PERSON> 'chromeZoomLevel' để zoom giao di<PERSON>n", "4. S<PERSON> dụng 'viewportScale' để điều chỉnh viewport", "5. <PERSON><PERSON><PERSON> adjustBrowserSize('mini'|'small'|'normal'|'large') trong runtime"], "tips": ["- Scale factor 0.75 (75%) là tối ưu nhất", "- Zoom level 0.75 giữ text đ<PERSON><PERSON> đ<PERSON>", "- Viewport scale 0.8 tương thích mobile", "- Sử dụng 'small' preset cho đa luồng"]}}