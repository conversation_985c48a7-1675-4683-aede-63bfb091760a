from Core import *
from src.modules.bypassCatpcha import *
from src.gologin import *
from src.hidemyacc import *

class StartChrome(QtCore.QThread):
    EnableStart          = QtCore.pyqtSignal(int, object, int)
    startMining          = QtCore.pyqtSignal(int, str, int)
    stopMining           = QtCore.pyqtSignal(int)
    setItem              = QtCore.pyqtSignal(int, int, str, object)
    statusBar            = QtCore.pyqtSignal(str, int)
    editCellByColumnName = QtCore.pyqtSignal(int, str, str, object)
    
    def __init__(self, gui, index, type = '', totalThread = ''):
        super().__init__()
        self.gui, self.index, self.typeThread, self.totalThread = gui, index, type, totalThread
        self.__jobLap = [];self.__result = 0;self.__total = 0;self.__publicLove = 0;self.__faildGetXu = 0;self.__faildClickJob = 0;self.__dem = 0;self.__jobDie = []; self.__cache = 0;self.__lstIDTTC = ''; self.__rateJobs = 0; self.__totalCoin = 0; self.__reopenChrome = 0; self.__error = 0
        self.followCoinTDS = 1300; self.loveCoinTDS = 500
        self.followCoinTTC = 1400; self.loveCoinTTC = 500
        self.status = 'Tòn đẹp trai'; self.dataAccTikTok = ''; self.xuHT = {'xu':0}; self.iterAcc = False
        self._slow_network = False  # Biến để theo dõi tình trạng mạng chậm

        self.url_tiktok = 'https://www.tiktok.com/'
    
    def __updateValue(self):
        acc = []
        for i in range(self.gui.tableWidget.columnCount()):
            try:
                acc += [self.gui.tableWidget.item(self.index, i).text()]
            except: acc += ['']
        self.stt, self.userTikTok, self.passTikTok, self.cookieTik, self.usernameTik, self.userCoin, self.passCoin, self.xuHt, self.xuThem, self.rate, self.total, self.idProfile, self.proxyChrome, self.status, self.action = acc[0], acc[1], acc[2], acc[3], acc[4], acc[5], acc[6], acc[7], acc[8], acc[9], acc[10], acc[11], acc[12].strip(), acc[13], acc[14]
        try:self.proxyRequests = self.settings['proxyImport'].strip().split('\n')[self.index]
        except:self.proxyRequests = ''

    def __handle_exceptions(self, e):
        logging.basicConfig(filename=errorlog, level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
        logging.exception("Exception occurred: {}".format(e))
    
    def __checkElement(self, typeBy: object, source: str, timeDelay: int):
        try:
            wait = WebDriverWait(self.driver, timeDelay)
            wait.until(EC.presence_of_element_located((typeBy, source)))
            return True
        except TimeoutException:
            return False
    
    def __moveToElementClick(self, by: By, value: str, text: str=...):
        ac = ActionChains(self.driver)
        try:
            el = WebDriverWait(self.driver, 20).until(EC.element_to_be_clickable((by, value)))
        except:
            self.driver.implicitly_wait(5)
            el = self.driver.find_element(by, value)
        ac.move_to_element(el).click(el).perform()
        if text != ...: ac.send_keys_to_element(el, text).perform()
    
    def __clickJS(self, el):
        self.driver.execute_script("arguments[0].click();", el)

    def showLoginPopupByHeartClick(self):
        """
        Hiển thị popup login bằng cách click nút tim 5 lần liên tiếp
        Returns:
            bool: True nếu popup login xuất hiện, False nếu thất bại
        """
        try:
            # Danh sách các selector để tìm nút tim/like
            heart_selectors = [
                '//*[@data-e2e="like-icon"]',  # Selector chính được sử dụng trong code hiện tại
                '//button[contains(@class, "like")]',
                '//div[contains(@class, "like")]',
                '//*[contains(@aria-label, "like")]',
                '//*[contains(@aria-label, "tim")]',
                '//span[contains(@class, "heart")]',
                '//i[contains(@class, "heart")]',
                '//*[@data-e2e="browse-like-icon"]',
                '//*[@data-e2e="video-like-icon"]'
            ]

            heart_element = None

            # Tìm nút tim bằng các selector khác nhau (tăng tốc độ)
            for selector in heart_selectors:
                try:
                    if self.__checkElement(By.XPATH, selector, 0.5):  # Giảm timeout từ 2s xuống 0.5s
                        heart_element = self.driver.find_element(By.XPATH, selector)
                        break
                except Exception as e:
                    continue

            # Nếu không tìm thấy bằng XPATH, thử CSS selector
            if heart_element is None:
                css_selectors = [
                    '[data-e2e="like-icon"]',
                    '[data-e2e="browse-like-icon"]',
                    '[data-e2e="video-like-icon"]',
                    'button[aria-label*="like"]',
                    'button[aria-label*="tim"]',
                    '.like-button',
                    '.heart-icon'
                ]

                for css_selector in css_selectors:
                    try:
                        heart_element = self.driver.find_element(By.CSS_SELECTOR, css_selector)
                        break
                    except Exception as e:
                        continue

            if heart_element is None:
                return False

            # Click nút tim 5 lần liên tiếp
            for i in range(5):
                try:
                    # Scroll đến element để đảm bảo nó hiển thị
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", heart_element)
                    time.sleep(0.1)  # Giảm delay từ 0.3s xuống 0.1s

                    # Thử click bằng ActionChains trước
                    try:
                        ActionChains(self.driver).move_to_element(heart_element).click().perform()
                    except:
                        # Nếu ActionChains thất bại, dùng JavaScript click
                        self.__clickJS(heart_element)

                    time.sleep(0.2)  # Giảm delay từ 0.5s xuống 0.2s

                except Exception as e:
                    continue

            # Đợi một chút để popup có thể xuất hiện
            time.sleep(1)  # Giảm từ 2s xuống 1s

            # Kiểm tra xem popup login có xuất hiện không
            login_popup_selectors = [
                '//div[contains(@class,"login-modal")]',
                '//div[contains(@class,"modal") and contains(.,"đăng nhập")]',
                '//div[contains(@class,"modal") and contains(.,"login")]',
                '//div[contains(@class,"popup") and contains(.,"đăng nhập")]',
                '//div[contains(@class,"popup") and contains(.,"login")]',
                '//*[contains(text(),"Đăng nhập")]',
                '//*[contains(text(),"Login")]'
            ]

            for popup_selector in login_popup_selectors:
                try:
                    if self.__checkElement(By.XPATH, popup_selector, 0.5):  # Giảm timeout từ 1s xuống 0.5s
                        return True
                except Exception as e:
                    continue

            # Kiểm tra bằng CSS selector
            css_popup_selectors = [
                '.login-modal',
                '.modal[class*="login"]',
                '.popup[class*="login"]',
                '[class*="login-modal"]',
                '[class*="login-popup"]'
            ]

            for css_popup_selector in css_popup_selectors:
                try:
                    popup_element = self.driver.find_element(By.CSS_SELECTOR, css_popup_selector)
                    if popup_element.is_displayed():
                        return True
                except Exception as e:
                    continue

            return False

        except Exception as e:
            self.__handle_exceptions(f"[ERROR] showLoginPopupByHeartClick: {e}")
            return False

    def __checkInternet(self, type = 'chrome'):
        for t in range(50):
            try:
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra kết nối internet.',self.gui.tableWidget)
                if type == 'chrome':
                    try:
                        # Đo tốc độ load trang để phát hiện mạng chậm
                        start_time = time.time()
                        pagesrc = self.driver.page_source
                        load_time = time.time() - start_time

                        # Nếu load trang mất > 3 giây thì coi là mạng chậm
                        if load_time > 3:
                            self._slow_network = True
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Phát hiện mạng chậm, tự động điều chỉnh timeout.',self.gui.tableWidget)
                        else:
                            self._slow_network = False

                        if 'Không thể truy cập trang web này' in self.driver.page_source or 'Không có kết nối Internet. Vui lòng thử lại.' in self.driver.page_source or 'Không có Internet' in self.driver.page_source or 'Ôi, hỏng!' in self.driver.page_source or 'Tải lại' in self.driver.page_source or 'Access Denied' in self.driver.page_source:
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Mất kết nối internet :<',self.gui.tableWidget)
                            self.driver.refresh()
                        else:
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kết nối internet ổn định.',self.gui.tableWidget)
                            break
                    except:self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Ôi, hỏng refresh để tiếp tục!',self.gui.tableWidget)
                else:
                    try:
                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra kết nối internet requests :>',self.gui.tableWidget)
                        start_time = time.time()
                        rsp = requests.get('https://www.google.com/', timeout=10)
                        load_time = time.time() - start_time

                        # Phát hiện mạng chậm qua requests
                        if load_time > 5:
                            self._slow_network = True
                        else:
                            self._slow_network = False
                        break
                    except:self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Mất kết nối internet requests :<',self.gui.tableWidget)
                time.sleep(5);self.driver.set_page_load_timeout(30)
            except:pass

    def __getPathImages(self):
        filePng = []
        for t in range(3):
            try:
                for root, dirs, files in os.walk(r'{}'.format(os.getcwd()+'/images/avatar')):
                    for file in files:
                        if file.endswith('.png'):
                            filePng.append(file)
                        elif file.endswith('.jpg'):
                            filePng.append(file)
                        elif file.endswith('.jpge'):
                            filePng.append(file)
                try:
                    return r"{}/{}".format(os.getcwd()+'/images/avatar', random.choice(filePng))
                except:pass
            except Exception as e:pass
        return False
    
    def __getPathVideos(self):
        fileMP4 = []
        for t in range(3):
            try:
                for root, dirs, files in os.walk(r'{}'.format(os.getcwd()+'/images/video')):
                    for file in files:
                        if file.endswith('.mp4'):
                            fileMP4.append(file)
                try:
                    return r"{}/{}".format(os.getcwd()+'/images/video', random.choice(fileMP4))
                except:pass
            except Exception as e:pass

    def __blockImage(self, type):
        try:
            current_title = self.driver.title
            self.driver.execute_script(f'document.title = "PYTOURNES:{type}"')
            time.sleep(2)
            self.driver.execute_script('document.title = "{}"'.format(current_title))
        except:pass

    def adjustBrowserSize(self, size_type='normal'):
        """
        Điều chỉnh kích thước browser với nhiều tùy chọn
        size_type: 'mini', 'small', 'normal', 'large'
        """
        try:
            if size_type == 'mini':
                # Thu nhỏ tối đa nhưng vẫn đọc được
                self.driver.execute_script("""
                    document.body.style.zoom = '0.6';
                    document.body.style.transform = 'scale(0.6)';
                    document.body.style.transformOrigin = 'top left';
                    document.body.style.width = '166.67%';
                    document.body.style.height = '166.67%';
                """)
                # Resize window
                self.driver.set_window_size(400, 600)

            elif size_type == 'small':
                # Thu nhỏ vừa phải
                self.driver.execute_script("""
                    document.body.style.zoom = '0.75';
                    document.body.style.transform = 'scale(0.75)';
                    document.body.style.transformOrigin = 'top left';
                    document.body.style.width = '133.33%';
                    document.body.style.height = '133.33%';
                """)
                self.driver.set_window_size(600, 800)

            elif size_type == 'normal':
                # Kích thước bình thường
                self.driver.execute_script("""
                    document.body.style.zoom = '1';
                    document.body.style.transform = 'scale(1)';
                    document.body.style.transformOrigin = 'top left';
                    document.body.style.width = '100%';
                    document.body.style.height = '100%';
                """)
                self.driver.set_window_size(800, 1000)

            elif size_type == 'large':
                # Kích thước lớn
                self.driver.execute_script("""
                    document.body.style.zoom = '1.2';
                    document.body.style.transform = 'scale(1.2)';
                    document.body.style.transformOrigin = 'top left';
                """)
                self.driver.set_window_size(1000, 1200)

            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now()}] TTC browser size adjusted to: {size_type}\n")

        except Exception as e:
            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now()}] TTC browser size adjustment failed: {e}\n")

    def testSelectorWithScale(self):
        """
        Test selector hoạt động với scale nhỏ
        """
        try:
            # Get current scale
            scale_factor = self.settings.get('chromeScaleFactor', 1.0)

            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now()}] TTC testing selectors with scale: {scale_factor}\n")

            # Test comment input selectors
            test_selectors = [
                'div[data-e2e="comment-input"] div[contenteditable="true"]',
                'div[data-e2e="comment-input"]',
                'div[role="textbox"][contenteditable="true"]',
                '[data-e2e="comment-post"]'
            ]

            working_selectors = []
            for selector in test_selectors:
                try:
                    if self.__checkElement(By.CSS_SELECTOR, selector, 0.5):
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        # Test if element is clickable
                        location = element.location
                        size = element.size
                        is_displayed = element.is_displayed()

                        working_selectors.append({
                            'selector': selector,
                            'location': location,
                            'size': size,
                            'displayed': is_displayed
                        })

                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] TTC selector working: {selector} at {location} size {size}\n")
                except Exception as e:
                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] TTC selector failed: {selector} - {e}\n")

            return working_selectors

        except Exception as e:
            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now()}] TTC selector test failed: {e}\n")
            return []

    def __changeProxy(self, type):
        try:
            if self.proxyChrome != '':
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ CHROME ] Change IP: {self.proxyChrome}',self.gui.tableWidget)
                if   type == 'proxyOn':self.driver.execute_script(f'document.title = "PYTOURNES:PROXY_ON:{self.proxyChrome}"')
                elif type == 'proxyOff':self.driver.execute_script(f'document.title = "PYTOURNES:PROXY_OFF"')
                time.sleep(3)
            
        except Exception as e: self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Có lỗi xảy ra khi thay đổi IP Chrome!!!',self.gui.tableWidget);print(e);self.__handle_exceptions(e)
    
    def safe_rmtree(self, directory):
        try:shutil.rmtree(directory)
        except Exception as e:print(f"Error deleting directory {directory}: {e}")

    def setupChrome(self):
        for t in range(3):
            try:
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {t} ] Khởi động Trình Duyệt...',self.gui.tableWidget)
                self.__updateValue()
                # Get POS Chrome
                self.xChrome = 0; self.yChrome = 0; self.wChrome = self.settings['widthChrome']; self.hChrome = self.settings['heightChrome']
                getWindowParams(self.wChrome, self.hChrome)
                try:
                    self.window_params = get_unused_window_params()
                    self.indexChrome, self.xChrome, self.yChrome, self.wChrome, self.hChrome = self.window_params
                except: self.indexChrome, self.xChrome, self.yChrome, self.wChrome, self.hChrome = 0,0, 0, 700, 1000
                # - - - - - - - - - - - - - - - - - - - - -
                # Options Chrome
                op = webdriver.ChromeOptions()
                op.add_argument('--lang=vi-VN')
                if self.settings['Browser'] == 'GoLogin':
                    self.__gologin = GoLogin({
                        "token": self.settings['pathChrome'],
                        "profile_id": self.idProfile.strip(),
                        "port": getRandomPort()
                        })
                    debugger_address = self.__gologin.start()
                    op.add_experimental_option("debuggerAddress", debugger_address)
                elif self.settings['Browser'] == 'HideMyAcc':
                    self.__hidemyacc = Hidemyacc()
                    debugger_address = self.__hidemyacc.start(self.idProfile.strip())
                    op.add_experimental_option("debuggerAddress", debugger_address)
                else:
                    op.add_argument("--disable-logging")
                    op.add_argument("--disable-dev-shm-usage")
                    op.add_argument('--lang=vi')
                    op.add_argument('--log-level=3')
                    op.add_argument("--mute-audio")
                    op.add_argument('--disable-blink-features=AutomationControlled')
                    op.add_argument("--disable-notifications")
                    op.add_argument("--disable-infobars")
                    op.add_argument('--disable-features=Translate')
                    op.add_argument("--disable-popup-blocking")
                    op.add_argument("--autoplay-policy-no-user-gesture-required")
                    op.add_argument('--no-default-browser-check')
                    op.add_argument('--force-dark-mode')
                    op.add_argument('--force-show-cursor')
                    
                    # Enhanced minisize options with popup-avoiding scaling
                    if self.settings['minisizeChrome']:
                        # Smart scale factor selection
                        scale_factor = self.settings.get('chromeScaleFactor', 0.6)  # Default 60% - avoids popup

                        # Special popup-avoiding scale factors
                        popup_avoiding_scales = [0.6, 0.65, 0.7]  # These scales tend to avoid popup
                        if scale_factor not in popup_avoiding_scales:
                            scale_factor = 0.6  # Force popup-avoiding scale

                        op.add_argument(f'--force-device-scale-factor={scale_factor}')

                        # Additional options for better minisize experience and popup avoidance
                        op.add_argument('--disable-features=TranslateUI,KeyboardShortcuts')  # Disable keyboard shortcuts
                        op.add_argument('--disable-component-extensions-with-background-pages')
                        op.add_argument('--disable-background-timer-throttling')
                        op.add_argument('--disable-renderer-backgrounding')
                        op.add_argument('--disable-backgrounding-occluded-windows')
                        op.add_argument('--disable-features=VizDisplayCompositor')  # Reduce UI complexity

                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] TTC using popup-avoiding scale factor: {scale_factor}\n")
                    prefs = {"credentials_enable_service": False,"profile.password_manager_enabled": False}
                    op.add_experimental_option("prefs", prefs)
                    op.binary_location = os.path.abspath(self.settings['pathChrome'])
                    # - - - - - - - - - - - - - - - - - - - - -

                    # Profile Chrome
                    path = pathConfig+'\\chrome\\'+self.userCoin
                    op.add_argument('--user-data-dir={}'.format(path))
                    # - - - - - - - - - - - - - - - - - - - - -
                    
                    # Tải nhiều Extension lên Chrome
                    try:
                        extension_folder = os.path.join(os.getcwd(), 'Extension')  # Đường dẫn đến thư mục Extension
                        true_items = [key for key, value in self.extension["ExtensionChecked"].items() if value]
                        full_paths = [os.path.join(extension_folder, item) for item in true_items]
                        result = ','.join(full_paths)
                        op.add_argument('--load-extension={}'.format(result))
                    except Exception as e:self.__handle_exceptions(e);self.status = 'Bật Extension lên đi!!!!'
                    # - - - - - - - - - - - - - - - - - - - - -

                # Path Chrome Driver
                if   self.settings['Browser'] == 'Thorium'   : self.__pathDriver = os.getcwd()+'\\Browser\\driver\\thorium\\chromedriver.exe'
                elif self.settings['Browser'] == 'GoLogin'   : self.__pathDriver = os.getcwd()+'\\Browser\\driver\\gologin\\chromedriver.exe'
                elif self.settings['Browser'] == 'Chrome'    : self.__pathDriver = os.getcwd()+'\\Browser\\driver\\chrome\\chromedriver.exe'
                elif self.settings['Browser'] == 'Brave'     : self.__pathDriver = os.getcwd()+'\\Browser\\driver\\brave\\chromedriver.exe'
                elif self.settings['Browser'] == 'HideMyAcc' : self.__pathDriver = os.getcwd()+'\\Browser\\driver\\hidemyacc\\chromedriver.exe'
                elif self.settings['Browser'] == 'Other'     : self.__pathDriver = os.getcwd()+'\\Browser\\driver\\other\\chromedriver.exe'
                # - - - - - - - - - - - - - - - - - - - - -

                s = Service(self.__pathDriver)
                # s.creation_flags = CREATE_NO_WINDOW
                self.driver = webdriver.Chrome(service=s ,  options=op ) #version_main=119
                if self.settings['Browser'] != 'GoLogin' and self.settings['Browser'] != 'HideMyAcc':self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {"source": "WebGLRenderingContext.prototype.getParameter = function(parameter) {if (parameter === 37445) {return 'VENDOR_INPUT';}if (parameter === 37446) {return 'RENDERER_INPUT';}return getParameter(parameter);};"})

                # Enhanced window positioning and sizing
                current_title = self.driver.title
                self.driver.execute_script(f'document.title = "ChromeON:{self.index+1}"')

                # Multiple minisize options
                if self.settings['widthChrome'] != 1920 and self.settings['Browser'] != 'GoLogin':
                    if self.settings['minisizeChrome']:
                        # Option 1: Scale factor minisize (keeps full interface)
                        self.driver.set_window_rect(self.xChrome, self.yChrome, self.wChrome, self.hChrome)

                        # Option 2: CSS zoom minisize (alternative method)
                        zoom_level = self.settings.get('chromeZoomLevel', 0.75)  # 75% zoom
                        self.driver.execute_script(f"""
                            document.body.style.zoom = '{zoom_level}';
                            document.body.style.transform = 'scale({zoom_level})';
                            document.body.style.transformOrigin = 'top left';
                        """)

                        # Option 3: Viewport minisize (keeps content readable)
                        viewport_scale = self.settings.get('viewportScale', 0.8)  # 80% viewport
                        self.driver.execute_script(f"""
                            var meta = document.createElement('meta');
                            meta.name = 'viewport';
                            meta.content = 'width=device-width, initial-scale={viewport_scale}, maximum-scale={viewport_scale}, user-scalable=no';
                            document.getElementsByTagName('head')[0].appendChild(meta);
                        """)
                    else:
                        # Normal size
                        self.driver.set_window_rect(self.xChrome, self.yChrome, self.wChrome, self.hChrome)
                    # time.sleep(1);win32gui.EnumWindows(self._enumWindows, None);time.sleep(1)
                # - - - - - - - - - - - - - - - - - - - - -

                self.__changeProxy('proxyOn')
                self.driver.get(self.url_tiktok);self.driver.set_page_load_timeout(10);time.sleep(3)
                self.actionChanins = ActionChains(self.driver)
                self.driver.execute_script('document.title = "{}"'.format(current_title))
                return True
            except Exception as e:
                self.status = f'Open Browser Error [ {e} ]';self.__handle_exceptions(self.status)
            time.sleep(5)
            if os.path.exists(pathChrome+'\\'+self.userCoin) and os.path.isdir(pathChrome+'\\'+self.userCoin):
                delete_thread = threading.Thread(target=self.safe_rmtree, args=(os.path.join(pathChrome, self.userCoin),))

        return False

    def _enumWindows(self, hwnd, _):
        if win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
            title = win32gui.GetWindowText(hwnd)
            if 'ChromeON' in title:
                hwnd = win32gui.FindWindow(None, title)
                # print(hwnd, title, self.indexChrome-1)
                win32gui.Setgui(hwnd, self.gui.view.hwnd[self.indexChrome])
                win32gui.MoveWindow(hwnd, 0, 0, 235, 385, True)

    def __switchWindow(self):
        self.__window = self.driver.window_handles[0]
        for t in range(10):
            for win in self.driver.window_handles:
                self.driver.switch_to.window(win)
                psrc = self.driver.page_source
                if 'identifier' in psrc or self.userTikTok in psrc:
                    return 
                else: 
                    time.sleep(1)
                    continue
            time.sleep(1)
        self.driver.switch_to.window(self.__window)

        for t in range(10):
            try:
                if 'Tài khoản của bạn đã bị đình chỉ' not in self.status:
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {t+1} ] Đăng nhập tài khoản [ {self.userTikTok} ]',self.gui.tableWidget)
                    try:
                        self.driver.get(self.url_tiktok)
                        self.driver.set_page_load_timeout(10)
                        time.sleep(3)
                        # Đảm bảo popup đăng nhập đã hiện
                        login_popup = False
                        try:
                            if self.__checkElement(By.XPATH, '//div[contains(@class,"login-modal")]'):
                                login_popup = True
                        except: pass

                        if not login_popup:
                            # Thử phương pháp mới: click nút tim 5 lần để hiển thị popup login
                            if self.showLoginPopupByHeartClick():
                                login_popup = True
                            else:
                                # Nếu phương pháp click nút tim không thành công, dùng phương pháp cũ
                                try:
                                    self.driver.execute_script('document.querySelector("button[data-e2e*=top-login-button], #header-login-button")?.click()')
                                    time.sleep(2)
                                except Exception as e:
                                    self.__handle_exceptions(f"[Coppy ERROR Send Admin] Inject JS login error: {e}")
                        # Bổ sung: Click nút 'Sử dụng số điện thoại/email/tên người dùng'
                        clicked_choose_user = False
                        try:
                            # Thử XPATH với timeout linh hoạt (tăng timeout khi mạng chậm)
                            timeout_choose = 3 if hasattr(self, '_slow_network') and self._slow_network else 1
                            if self.__checkElement(By.XPATH, '//div[contains(text(), "Sử dụng số điện thoại/email/tên người dùng")]', timeout_choose):
                                self.driver.find_element(By.XPATH, '//div[contains(text(), "Sử dụng số điện thoại/email/tên người dùng")]').click()
                                clicked_choose_user = True
                                time.sleep(0.8 if hasattr(self, '_slow_network') and self._slow_network else 0.5)
                        except Exception as e:
                            self.__handle_exceptions(f"[Coppy ERROR Send Admin] Click XPATH nút chọn user: {e}")
                        if not clicked_choose_user:
                            try:
                                # Thử CSS selector với retry cho mạng chậm
                                for retry in range(2):
                                    try:
                                        self.driver.execute_script('Array.from(document.querySelectorAll("div")).find(el => el.textContent.includes("Sử dụng số điện thoại") || el.textContent.includes("email") || el.textContent.includes("người dùng"))?.click()')
                                        clicked_choose_user = True
                                        break
                                    except:
                                        if retry == 0:
                                            time.sleep(1)  # Đợi thêm 1s cho mạng chậm
                                time.sleep(0.8 if hasattr(self, '_slow_network') and self._slow_network else 0.5)
                                self.__handle_exceptions("[Coppy ERROR Send Admin] Đã inject JS click nút chọn user")
                            except Exception as e:
                                self.__handle_exceptions(f"[Coppy ERROR Send Admin] JS click nút chọn user lỗi: {e}")

                        # 2. Chọn đăng nhập bằng email hoặc TikTok ID (nếu có)
                        try:
                            timeout_email = 3 if hasattr(self, '_slow_network') and self._slow_network else 1
                            if self.__checkElement(By.XPATH, '//a[contains(text(), "Đăng nhập bằng email") or contains(text(), "TikTok ID")]', timeout_email):
                                self.driver.find_element(By.XPATH, '//a[contains(text(), "Đăng nhập bằng email") or contains(text(), "TikTok ID")]').click()
                                time.sleep(0.8 if hasattr(self, '_slow_network') and self._slow_network else 0.5)
                        except Exception as e:
                            self.__handle_exceptions(f"[Coppy ERROR Send Admin] Click chọn đăng nhập email/ID: {e}")

                        # 3. Điền username với retry cho mạng chậm
                        filled_user = False
                        timeout_input = 3 if hasattr(self, '_slow_network') and self._slow_network else 1
                        try:
                            if self.__checkElement(By.XPATH, '//input[@name="username" or @autocomplete="username"]', timeout_input):
                                el = self.driver.find_element(By.XPATH, '//input[@name="username" or @autocomplete="username"]')
                                el.clear(); el.send_keys(self.userTikTok)
                                filled_user = True
                                time.sleep(0.5 if hasattr(self, '_slow_network') and self._slow_network else 0.3)
                        except Exception as e:
                            self.__handle_exceptions(f"[Coppy ERROR Send Admin] Điền username XPATH lỗi: {e}")
                        if not filled_user:
                            try:
                                # Retry với JS cho mạng chậm
                                for retry in range(2):
                                    try:
                                        self.driver.execute_script('document.querySelector("input[name=username], input[autocomplete=username]").value = arguments[0]', self.userTikTok)
                                        filled_user = True
                                        break
                                    except:
                                        if retry == 0:
                                            time.sleep(1)
                                time.sleep(0.5 if hasattr(self, '_slow_network') and self._slow_network else 0.3)
                            except Exception as e:
                                self.__handle_exceptions(f"[Coppy ERROR Send Admin] JS điền username lỗi: {e}")

                        # 4. Điền password với retry cho mạng chậm
                        filled_pass = False
                        try:
                            if self.__checkElement(By.XPATH, '//input[@type="password" or @placeholder="Mật khẩu"]', timeout_input):
                                el = self.driver.find_element(By.XPATH, '//input[@type="password" or @placeholder="Mật khẩu"]')
                                el.clear(); el.send_keys(self.passTikTok)
                                filled_pass = True
                                time.sleep(0.8 if hasattr(self, '_slow_network') and self._slow_network else 0.5)
                        except Exception as e:
                            self.__handle_exceptions(f"[Coppy ERROR Send Admin] Điền password XPATH lỗi: {e}")
                        if not filled_pass:
                            try:
                                # Retry với JS cho mạng chậm
                                for retry in range(2):
                                    try:
                                        self.driver.execute_script('document.querySelector("input[type=password], input[placeholder=\'Mật khẩu\']").value = arguments[0]', self.passTikTok)
                                        filled_pass = True
                                        break
                                    except:
                                        if retry == 0:
                                            time.sleep(1)
                                time.sleep(0.5 if hasattr(self, '_slow_network') and self._slow_network else 0.3)
                            except Exception as e:
                                self.__handle_exceptions(f"[Coppy ERROR Send Admin] JS điền password lỗi: {e}")

                        # 5. Click nút đăng nhập với timeout linh hoạt
                        clicked_login = False
                        timeout_login = 3 if hasattr(self, '_slow_network') and self._slow_network else 1
                        try:
                            if self.__checkElement(By.XPATH, '//button[@data-e2e="login-button"]', timeout_login):
                                self.driver.find_element(By.XPATH, '//button[@data-e2e="login-button"]').click()
                                clicked_login = True
                                time.sleep(3 if hasattr(self, '_slow_network') and self._slow_network else 2)
                        except Exception as e:
                            self.__handle_exceptions(f"[Coppy ERROR Send Admin] Click nút đăng nhập XPATH lỗi: {e}")
                        if not clicked_login:
                            try:
                                self.driver.execute_script('document.querySelector("button[data-e2e=login-button]")?.click()')
                                time.sleep(2 if hasattr(self, '_slow_network') and self._slow_network else 1)
                            except Exception as e:
                                self.__handle_exceptions(f"[Coppy ERROR Send Admin] JS click login button lỗi: {e}")
                        self.bypassCaptcha(3)
                        if 'Block-Media' in self.extension['ExtensionChecked']:
                            self.__blockImage('BLOCK_ON')
                        if self.__checkCookie():
                            return True
                    except Exception as e:
                        self.__handle_exceptions(f"[Coppy ERROR Send Admin] TikTok login step error: {e}")
            except Exception as e:
                self.status = f'Login TIKTOK Error [ {e} ]'
                self.__handle_exceptions(self.status)
                return False
        
    def uploadAvatar(self):
        for t in range(10):
            try:
                if 'Block-Media' in self.extension['ExtensionChecked']:self.__blockImage('BLOCK_ON')
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {t+1} ] Cập nhật ảnh đại diện [ {self.userTikTok} ]',self.gui.tableWidget)
                self.driver.get('https://www.tiktok.com/profile');self.driver.set_page_load_timeout(10);time.sleep(3)
                self.bypassCaptcha()
                if self.__checkElement(By.XPATH, '//span[text()="Sửa hồ sơ"]',3):
                    self.driver.find_element(By.XPATH, '//span[text()="Sửa hồ sơ"]').click();time.sleep(3)
                if self.__checkElement(By.XPATH, "//input[contains(@class, 'InputUpload')]",3):
                    
                    self.pathAvt = self.__getPathImages()
                    self.driver.find_element(By.XPATH, "//input[contains(@class, 'InputUpload')]").send_keys(self.pathAvt);time.sleep(1)
                    try:
                        if self.__checkElement(By.XPATH, '//button[text()="Đăng ký"]',3):
                            self.driver.find_element(By.XPATH, '//button[text()="Đăng ký"]').click();time.sleep(3)
                        if self.__checkElement(By.XPATH, '//button[text()="Lưu"]',3):
                            self.driver.find_element(By.XPATH, '//button[text()="Lưu"]').click();time.sleep(3)
                            return True
                    except:time.sleep(3)
                    time.sleep(3)
            except:pass        
    
    def checkVideoUpload(self):
        while True:
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra Video đã đăng lên chưa...',self.gui.tableWidget)
            time.sleep(0.25)
            try:
                if self.driver.find_element(By.XPATH, '//*[text()="Chỉnh sửa video"]').is_displayed():
                    return True
            except: continue

    def checkUploadSuccess(self):
        while True:
            
            time.sleep(0.25)
            self.driver.implicitly_wait(0.1)
            try:
                self.driver.find_element(By.XPATH, '//*[text()="Để sau"]').click()
            except: pass
            try:
                self.driver.find_elements(By.XPATH, '//*[text()="Tải video khác lên"]')[-1].click()
                return True
            except: continue

    def uploadVideo(self):
        for t in range(3):
            try:
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {t+1} ] Đăng video lên TikTok [ {self.userTikTok} ]',self.gui.tableWidget)
                self.driver.get('https://www.tiktok.com/creator-center/upload?from=upload');time.sleep(5)
                self.driver.switch_to.frame(self.driver.find_element(By.XPATH, '//iframe[@data-tt="Upload_index_iframe"]'))
                self.driver.find_element(By.XPATH,'//input[@accept="video/*"]').send_keys(self.__getPathVideos())
                self.checkVideoUpload()
                for i in range(20): 
                    try:
                        self.driver.find_element(By.XPATH, '//*[text()="Đăng" or text()="Lịch biểu"]').click()
                    except:pass
                    time.sleep(0.5)

                    if "Đã thử tải lên quá nhiều lần. Vui lòng thử lại sau." in self.driver.page_source:
                        self.status = 'Đã thử tải lên quá nhiều lần. Vui lòng thử lại sau.'
                        return False
                    elif "Video của bạn đang được tải lên TikTok!" in self.driver.page_source:
                        self.driver.find_element(By.XPATH, '//*[text()="Quản lý bài đăng của bạn"]').click()
                        time.sleep(5)
                        if 'Đã đăng' in self.driver.page_source:
                            self.status = 'Đăng video thành công.'
                            return True
                        elif 'Chưa có bài đăng nào' in self.driver.page_source or 'Không tìm thấy kết quả' in self.driver.page_source:
                            self.status = 'Đăng video thất bại!!!'
                            return False
                    elif "Việc bạn rời trang không gây gián đoạn cho quá trình đăng video" in self.driver.page_source or "Video đã được lên lịch!" in self.driver.page_source: 
                        if self.checkUploadSuccess():
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Đăng video thành công.',self.gui.tableWidget)
                            self.status = 'Đăng video thành công.'
                            time.sleep(5)
                            return True
                self.status = 'Đăng video thất bại!!!'
            except Exception as e:self.__handle_exceptions('Error upload Video'); self.status = 'Không tìm thấy XPATH //input[@type="file"]'
        
    def editBio(self):
         for t in range(10):
            try:
                if 'Block-Media' in self.extension['ExtensionChecked']:self.__blockImage('BLOCK_ON')
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {t+1} ] Cập nhật Bio [ {self.userTikTok} ]',self.gui.tableWidget)
                self.driver.get('https://www.tiktok.com/profile');self.driver.set_page_load_timeout(10);time.sleep(3)
                self.bypassCaptcha()
                if self.__checkElement(By.XPATH, '//span[text()="Sửa hồ sơ"]',3):
                    self.driver.find_element(By.XPATH, '//span[text()="Sửa hồ sơ"]').click();time.sleep(3)
                if self.__checkElement(By.XPATH, '//textarea[@placeholder="Tiểu sử"]',3):
                    time.sleep(3)
                    if 'Auto TikTok By PyTournes | https://pytournes.io.vn/' not in self.driver.page_source:
                        self.driver.find_element(By.XPATH, '//textarea[@placeholder="Tiểu sử"]').send_keys('Auto TikTok By PyTournes | https://pytournes.io.vn/');time.sleep(1)
                        try:
                            if self.__checkElement(By.XPATH, '//button[text()="Đăng ký"]',3):
                                self.driver.find_element(By.XPATH, '//button[text()="Đăng ký"]').click();time.sleep(3)
                            if self.__checkElement(By.XPATH, '//button[text()="Lưu"]',3):
                                self.driver.find_element(By.XPATH, '//button[text()="Lưu"]').click();time.sleep(3)
                                return True
                        except:time.sleep(3)
                        time.sleep(3)
                    else:return True
            except Exception as e:self.__handle_exceptions('Error edit bio tiktok')

    def bypassCaptcha(self, count = 8):
        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Kiểm tra Captcha TikTok',self.gui.tableWidget)
        time.sleep(3)
        for t in range(count):
            try:
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {t+1}/{count} ] Kiểm tra Captcha TikTok',self.gui.tableWidget)
                self.__pageSource = self.driver.page_source

                # Check for various captcha indicators
                captcha_selectors = [
                    'div.captcha_verify_message',
                    '.captcha-container',
                    '[class*="captcha"]',
                    '.verify-wrap',
                    '.secsdk-captcha-wrapper'
                ]

                captcha_found = False
                for selector in captcha_selectors:
                    if self.__checkElement(By.CSS_SELECTOR, selector, 1):
                        captcha_found = True
                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Phát hiện Captcha: {selector}',self.gui.tableWidget)
                        break

                # Check for captcha text indicators
                captcha_texts = [
                    'Không thể tải hình ảnh. Hãy làm mới để thử lại.',
                    'Please verify',
                    'Verify you are human',
                    'captcha',
                    'verification'
                ]

                for text in captcha_texts:
                    if text.lower() in self.__pageSource.lower():
                        captcha_found = True
                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Phát hiện Captcha text: {text[:20]}...',self.gui.tableWidget)
                        break

                if captcha_found:
                    # Try to handle captcha
                    if 'Block-Media' in self.extension['ExtensionChecked']:
                        if self.extension['ExtensionChecked']['Block-Media'] == True:
                            self.__blockImage('BLOCK_OFF')
                            time.sleep(2)

                    # Try various refresh/reload methods
                    refresh_selectors = [
                        'div.captcha_verify_action > div > a',
                        '//span[text()="Làm mới"]',
                        '//button[contains(text(), "Refresh")]',
                        '//button[contains(text(), "Reload")]',
                        '.captcha-refresh',
                        '.refresh-btn'
                    ]

                    for selector in refresh_selectors:
                        try:
                            if selector.startswith('//'):
                                element = self.driver.find_element(By.XPATH, selector)
                            else:
                                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                            element.click()
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Clicked refresh: {selector}',self.gui.tableWidget)
                            time.sleep(3)
                            break
                        except:
                            continue

                    # If no refresh button found, try page refresh
                    if captcha_found:
                        try:
                            self.driver.refresh()
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Làm mới trang để bypass captcha',self.gui.tableWidget)
                            time.sleep(5)
                        except:
                            pass
                    
                if 'Thiếu thông tin chứng nhận ID' in self.__pageSource:
                    self.status = 'Thiếu thông tin chứng nhận ID'
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget)
                    self.__deleteCookie();time.sleep(3)
                    return False
                    
                elif 'Tài khoản của bạn đã bị đình chỉ.'  in self.__pageSource:
                    self.status = 'Tài khoản của bạn đã bị đình chỉ.'
                    return False

                elif 'Mật khẩu của bạn đã hết hạn và phải được thay đổi để giữ an toàn cho tài khoản.' in self.__pageSource:
                    self.status = 'Mật khẩu của bạn đã hết hạn và phải được thay đổi để giữ an toàn cho tài khoản.'
                    return False
                
                elif 'Mật khẩu sai'  in self.__pageSource or 'Sai tài khoản hoặc mật khẩu' in self.__pageSource:
                    self.status = 'Sai tài khoản hoặc mật khẩu. Còn 5 lần nhập. Hãy thử lại.'
                    return False
                
                elif 'Rất tiếc, đã xảy ra lỗi, vui lòng thử lại sau' in self.__pageSource:
                    self.status ='Rất tiếc, đã xảy ra lỗi, vui lòng thử lại sau'
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget)
                    self.__deleteCookie();time.sleep(3)
                    return False
                    
                elif 'Bạn truy cập dịch vụ của chúng tôi quá thường xuyên.' in self.__pageSource:
                    self.status = 'Bạn truy cập dịch vụ của chúng tôi quá thường xuyên.'
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget)
                    self.__deleteCookie();time.sleep(3)
                    return False
                    
                elif 'Kéo thanh trượt để ghép hình' in self.__pageSource or 'Drag the slider' in self.__pageSource :
                    time.sleep(1);self.koleso()
                
                elif  'Xác minh để tiếp tục' in self.__pageSource or 'Verify to continue' in self.__pageSource:
                    time.sleep(1);self.slider()
                    
                elif 'Chọn 2 đối tượng có hình dạng giống nhau' in self.__pageSource or 'Select 2 objects that are the same shape' in self.__pageSource:
                    time.sleep(1);self.choose()
                    
                else:
                    self.__checkInternet()
                    if 'Block-Media' in self.extension['ExtensionChecked']:self.__blockImage('BLOCK_ON')
                    return True
                    
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget)
            except Exception as e:
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Có lỗi xảy ra khi giải captcha.',self.gui.tableWidget)
                self.__handle_exceptions(e)
        return False
    
    def koleso(self):
        try:
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Giải captcha Kéo thanh trượt để ghép hình',self.gui.tableWidget)
            src           = self.driver.find_elements(By.XPATH, '//img')
            border        = dow_img(src[len(src)-2].get_attribute("src"))
            inside        = dow_img(src[len(src)-1].get_attribute("src"))
            resultCapt, _ = Koleso(border, inside).main()
            if int(resultCapt) == 0:
                self.driver.find_element(By.CSS_SELECTOR, 'div.captcha_verify_action > div > a').click()
                return self.bypassCaptcha(3)
            if self.__checkElement(By.XPATH,"//div[@class='secsdk-captcha-drag-icon sc-hMqMXs VZMN']",3):
                el     = self.driver.find_element(By.XPATH,"//div[@class='secsdk-captcha-drag-icon sc-hMqMXs VZMN']")
            else:
                el     = self.driver.find_element(By.XPATH,"//div[@class='secsdk-captcha-drag-icon sc-kEYyzF fiQtnm']")
            self.actionChanins.click_and_hold(el).perform()
            for i in range(5):
                self.actionChanins.move_by_offset(round(float(resultCapt*PIXEL))/5, 0)
                self.actionChanins.pause(0.3)
            self.actionChanins.release()
            self.actionChanins.perform()
            time.sleep(1)
        except Exception as e:
            self.__handle_exceptions('Koleso captcha error')
        self.bypassCaptcha(3)
    
    def slider(self):
        try:
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Giải captcha Xác minh để tiếp tục',self.gui.tableWidget)
            for count in range(5, 10):
                try:
                    bg    = self.driver.find_element(By.XPATH, f'/html/body/div[{count}]/div/div[2]/img[1]') # Background captcha
                    slide = self.driver.find_element(By.XPATH, f'/html/body/div[{count}]/div/div[2]/img[2]') #Slide captcha
                except:pass
            src        = self.driver.find_elements(By.XPATH, '//img')
            img_bg     = dow_img_the_same(bg.get_attribute("src"))
            img_slide  = dow_img_the_same(slide.get_attribute("src"))
            size_slide = (slide.size['width'], slide.size['height'])
            size_bg    = (bg.size['width'], bg.size['height'])
            resultCapt = Slider(size_slide, size_bg, img_slide, img_bg).get_position()
            resultCapt = (resultCapt - 32)
            if self.__checkElement(By.XPATH,"//div[@class='secsdk-captcha-drag-icon sc-hMqMXs VZMN']",3):
                el     = self.driver.find_element(By.XPATH,"//div[@class='secsdk-captcha-drag-icon sc-hMqMXs VZMN']")
            else:
                el     = self.driver.find_element(By.XPATH,"//div[@class='secsdk-captcha-drag-icon sc-kEYyzF fiQtnm']")
            if int(resultCapt) == 0:
                self.driver.find_element(By.CSS_SELECTOR, 'div.captcha_verify_action > div > a').click()
                return self.bypassCaptcha(3)
            self.actionChanins.click_and_hold(el).perform()
            for i in range(5):
                self.actionChanins.move_by_offset(int(resultCapt / 5),0)
                self.actionChanins.pause(0.3)
            self.actionChanins.release()
            self.actionChanins.perform()  
        except Exception as e:
            self.__handle_exceptions('Slider captcha error')
        self.bypassCaptcha(3)
    
    def choose(self):
        try:
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Giải captcha Chọn 2 đối tượng có hình dạng giống nhau',self.gui.tableWidget)
            src   = self.driver.find_elements(By.XPATH, '//img')
            slove = Choose(src[len(src)-1].get_attribute("src"))
            pos   = slove.V2('pos')
            if pos != False:
                p1, p2 = pos
                x1,y1  = p1[0], p1[1]
                x2,y2  = p2[0], p2[1]
                el     = self.driver.find_element('xpath', "//div[contains(@class, 'captcha_verify_img')]")
                w, h   = -el.size["width"], el.size["height"]
                self.actionChanins.move_to_element_with_offset(el, w/2, h/2).move_by_offset(x1, -h+y1).click().perform()
                self.actionChanins.move_to_element_with_offset(el, w/2, h/2).move_by_offset(x2, -h+y2).click().perform()
                if self.__checkElement(By.XPATH, '//div[text()="Xác nhận"]',1) : self.driver.find_element(By.XPATH, f'//div[text()="Xác nhận"]').click()
                if self.__checkElement(By.XPATH, '//div[text()="Confirm"]',1)  : self.driver.find_element(By.XPATH, f'//div[text()="Confirm"]').click()
                time.sleep(1)
            else:
                self.driver.find_element(By.CSS_SELECTOR, 'div.captcha_verify_action > div > a').click()
                return self.bypassCaptcha(3)
        except Exception as e:
            self.__handle_exceptions('Choose captcha error')
        self.bypassCaptcha(3)
    
    def __closePopups(self):
        """Close TikTok popups that might block comment input - Enhanced for keyboard shortcuts popup"""

        # First, try to close the specific "Giới thiệu các phím tắt trên bàn phím!" popup
        keyboard_shortcuts_xpaths = [
            # Specific XPath for keyboard shortcuts popup close button
            '//div[contains(text(), "Giới thiệu các phím tắt")]//ancestor::div[contains(@class, "modal") or contains(@class, "popup")]//button[contains(@aria-label, "close") or contains(@aria-label, "Close") or contains(@aria-label, "đóng") or contains(@aria-label, "Đóng")]',
            '//div[contains(text(), "Keyboard shortcuts")]//ancestor::div[contains(@class, "modal") or contains(@class, "popup")]//button',
            '//div[contains(text(), "phím tắt")]//ancestor::div[contains(@class, "modal")]//button',
            # Generic modal close buttons
            '//div[contains(@class, "modal")]//button[contains(@aria-label, "close") or contains(@aria-label, "Close")]',
            '//div[contains(@class, "modal")]//button[text()="×" or contains(@class, "close")]'
        ]

        # Try XPath selectors first for keyboard shortcuts popup
        for xpath_selector in keyboard_shortcuts_xpaths:
            try:
                if self.__checkElement(By.XPATH, xpath_selector, 0.5):
                    popup_button = self.driver.find_element(By.XPATH, xpath_selector)
                    # Try multiple click methods for better reliability
                    try:
                        ActionChains(self.driver).move_to_element(popup_button).click().perform()
                    except:
                        try:
                            popup_button.click()
                        except:
                            self.driver.execute_script("arguments[0].click();", popup_button)

                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] TTC closed keyboard shortcuts popup with XPath\n")
                    time.sleep(0.8)
                    return  # Exit if successfully closed
            except Exception as e:
                continue

        # Enhanced CSS selectors for various popups
        popup_selectors = [
            # Keyboard shortcuts popup specific selectors
            'div[class*="modal"] button[aria-label*="close"]',
            'div[class*="modal"] button[aria-label*="Close"]',
            'div[class*="modal"] button[aria-label*="đóng"]',
            'div[class*="modal"] button[aria-label*="Đóng"]',
            'div[class*="popup"] button[aria-label*="close"]',
            'div[class*="popup"] button[aria-label*="Close"]',
            # Original selectors
            'button[data-e2e="modal-close-inner"]',
            'div[data-e2e="modal-close"]',
            '[aria-label="Close"]',
            '[aria-label="close"]',
            '[aria-label="Đóng"]',
            '[aria-label="đóng"]',
            'button:contains("×")',
            'button:contains("Close")',
            'button:contains("Đóng")',
            # Browser recovery popup
            'button:contains("Khôi phục")',
            'button:contains("Không")',
            'button:contains("No")',
            # Generic close buttons
            '.close-button',
            '.modal-close',
            '[role="button"][aria-label*="close"]',
            '[role="button"][aria-label*="Close"]'
        ]

        for selector in popup_selectors:
            try:
                if self.__checkElement(By.CSS_SELECTOR, selector, 0.5):
                    popup_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    # Enhanced click method
                    try:
                        ActionChains(self.driver).move_to_element(popup_button).click().perform()
                    except:
                        try:
                            popup_button.click()
                        except:
                            self.driver.execute_script("arguments[0].click();", popup_button)

                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                        f.write(f"[{datetime.now()}] TTC closed popup with CSS: {selector}\n")
                    time.sleep(0.5)
            except Exception as e:
                continue

        # Press Escape key multiple times to close any remaining popups
        try:
            from selenium.webdriver.common.keys import Keys
            body = self.driver.find_element(By.TAG_NAME, 'body')
            for i in range(3):  # Try Escape 3 times for stubborn popups
                body.send_keys(Keys.ESCAPE)
                time.sleep(0.3)
            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now()}] TTC tried Escape key 3 times to close popups\n")
        except Exception as e:
            pass

    def __clickXmlJobs(self, job_comment_content=None):
        if self.driver.current_url in self.__link:
            if self.__typeJob == 'love':
                try:
                    if '?' in self.__link:self.idclick = self.__link.split('video/')[1].split('?')[0]
                    elif 'photo' in self.__link:self.idclick = self.__link.split('photo/')[1]
                    else: self.idclick = self.__link.split('video/')[1]

                    self.__hide = 0
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {self.__demJob+1}/{self.countJob} ] {self.__typeJob.upper()} tài khoản @{self.__link.split("@")[1].split("/")[0]}',self.gui.tableWidget)
                    
                    self.delaySettings('click')
                    for t in range(3):
                        try:
                            self.driver.find_element(By.ID,f'xgwrapper-4-{self.idclick}').click()
                        except:
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Không tìm thấy nút {self.__typeJob.upper()}!!!',self.gui.tableWidget)
                            if self.__checkElement(By.XPATH,'//*[@data-e2e="like-icon"]',2):
                                self.driver.find_element(By.XPATH,'//*[@data-e2e="like-icon"]').click()
                            break
                    if self.settings['bypassCaptcha'] == False:self.bypassCaptcha(3)
                    self.__jobLap.append(self.__link)
                    return True
                except Exception as e: self.__handle_exceptions('Error Love Account');self.__hide = 1; return False
            
            elif self.__typeJob == 'follow':
                try:
                    if '@' in self.__link: 
                        self.__link = self.__link.split("@")[1]
                    
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {self.__demJob+1}/{self.countJob} ] {self.__typeJob.upper()} tài khoản @{self.__link}',self.gui.tableWidget)
                    time.sleep(0.5)

                    # Thử nhiều cách để tìm nút Follow
                    follow_found = False
                    
                    # 1. Tìm bằng text "Follow"
                    if self.__checkElement(By.XPATH,'//button[text()="Follow"]', 6):
                        self.delaySettings('follow')
                        self.driver.find_element(By.XPATH,'//button[text()="Follow"]').click()
                        follow_found = True
                    
                    # 2. Tìm bằng data-e2e attribute
                    elif self.__checkElement(By.CSS_SELECTOR,'[data-e2e="follow-button"]', 3):
                        self.delaySettings('follow') 
                        self.driver.find_element(By.CSS_SELECTOR,'[data-e2e="follow-button"]').click()
                        follow_found = True
                        
                    # 3. Tìm bằng class name
                    elif self.__checkElement(By.CSS_SELECTOR,'button.follow-button,button.jsx-follow-button', 3):
                        self.delaySettings('follow')
                        self.driver.find_element(By.CSS_SELECTOR,'button.follow-button,button.jsx-follow-button').click()
                        follow_found = True
                        
                    # 4. Thử tìm bằng XPath phức tạp hơn
                    elif self.__checkElement(By.XPATH,"//button[contains(@class, 'follow') or contains(@class, 'Follow')]", 3):
                        self.delaySettings('follow')
                        self.driver.find_element(By.XPATH,"//button[contains(@class, 'follow') or contains(@class, 'Follow')]").click()
                        follow_found = True

                    if follow_found:
                        if self.settings['checkVirtual'] == 'Đợi 6 giây': 
                            time.sleep(6)
                        if self.settings['bypassCaptcha'] == False:
                            self.bypassCaptcha(3)
                        if self.settings['checkVirtual'] == 'Reload trang':
                            time.sleep(random.randint(1,3))
                            self.driver.refresh()
                        if self.settings['checkVirtual'] == 'Không kiểm tra':
                            return True
                            
                        # Verify the follow action worked
                        if not self.__checkElement(By.XPATH,'//button[text()="Follow"]',3):
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Follow tài khoản @{self.__link} thành công!',self.gui.tableWidget)
                            return True
                    
                    self.__faildClickJob += 1
                    self.status = f'Có {self.__faildClickJob} lần làm nhiệm vụ thất bại chuyển tới tài khoản tiếp theo.'
                        
                except Exception as e:
                    self.__handle_exceptions('Error Follow Account {}'.format(self.__link))
                    try:
                        self.driver.get(self.url_tiktok)
                        self.driver.refresh()
                        self.driver.set_page_load_timeout(30)
                    except:
                        pass
                return False

            elif self.__typeJob == 'cmt' or self.__typeJob == 'comment':
                try:
                    if '?' in self.__link:self.idclick = self.__link.split('video/')[1].split('?')[0]
                    elif 'photo' in self.__link:self.idclick = self.__link.split('photo/')[1]
                    else: self.idclick = self.__link.split('video/')[1]

                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {self.__demJob+1}/{self.countJob} ] {self.__typeJob.upper()} video {self.__link.split("@")[1].split("/")[0]}',self.gui.tableWidget)

                    # Get comment content from API (phù hợp với video cụ thể)
                    comment_text = ""

                    if job_comment_content:
                        try:
                            import json
                            comments_list = json.loads(job_comment_content)
                            if comments_list and len(comments_list) > 0:
                                import random
                                comment_text = random.choice(comments_list)
                                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Comment phù hợp: "{comment_text[:30]}..."',self.gui.tableWidget)
                            else:
                                comment_text = "Nice video!"
                                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'API không có comment, dùng mặc định',self.gui.tableWidget)
                        except Exception as e:
                            comment_text = "Nice video!"
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Parse comment lỗi: {e}',self.gui.tableWidget)
                    else:
                        comment_text = "Nice video!"
                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Không có comment từ API',self.gui.tableWidget)

                    self.delaySettings('click')

                    # Close any popups that might block comment input (especially keyboard shortcuts popup)
                    self.__closePopups()

                    # Enhanced popup detection and closing - multiple aggressive methods
                    try:
                        # Method 1: Check for keyboard shortcuts popup by text
                        popup_detected = False
                        popup_texts = [
                            "Giới thiệu các phím tắt",
                            "Keyboard shortcuts",
                            "phím tắt trên bàn phím",
                            "shortcuts on keyboard"
                        ]

                        for popup_text in popup_texts:
                            if self.__checkElement(By.XPATH, f'//div[contains(text(), "{popup_text}")]', 0.3):
                                popup_detected = True
                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC detected popup by text: {popup_text}\n")
                                break

                        # Method 2: Check for modal/popup containers
                        if not popup_detected:
                            modal_selectors = [
                                'div[class*="modal"][style*="display: block"]',
                                'div[class*="popup"][style*="display: block"]',
                                'div[class*="Modal"]',
                                'div[class*="Popup"]',
                                '[role="dialog"]',
                                '[role="modal"]'
                            ]
                            for modal_sel in modal_selectors:
                                if self.__checkElement(By.CSS_SELECTOR, modal_sel, 0.2):
                                    popup_detected = True
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC detected popup by selector: {modal_sel}\n")
                                    break

                        if popup_detected:
                            # Aggressive popup closing methods
                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                f.write(f"[{datetime.now()}] TTC starting aggressive popup closing\n")

                            # Method A: Try clicking X button
                            close_selectors = [
                                'button[aria-label*="close" i]',
                                'button[aria-label*="đóng" i]',
                                'button[class*="close"]',
                                '[data-testid*="close"]',
                                'button:contains("×")',
                                'button:contains("✕")',
                                'svg[class*="close"]'
                            ]

                            for close_sel in close_selectors:
                                try:
                                    if self.__checkElement(By.CSS_SELECTOR, close_sel, 0.2):
                                        close_btn = self.driver.find_element(By.CSS_SELECTOR, close_sel)
                                        self.driver.execute_script("arguments[0].click();", close_btn)
                                        time.sleep(0.3)
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC closed popup with close button: {close_sel}\n")
                                        break
                                except:
                                    continue

                            # Method B: Multiple ESC key presses
                            from selenium.webdriver.common.keys import Keys
                            body = self.driver.find_element(By.TAG_NAME, 'body')
                            for i in range(10):  # Increased from 5 to 10
                                body.send_keys(Keys.ESCAPE)
                                time.sleep(0.1)

                            # Method C: Click outside modal (on backdrop)
                            try:
                                self.driver.execute_script("""
                                    var modals = document.querySelectorAll('[class*="modal"], [class*="popup"], [role="dialog"]');
                                    modals.forEach(function(modal) {
                                        if (modal.style.display !== 'none') {
                                            modal.style.display = 'none';
                                            modal.remove();
                                        }
                                    });
                                """)
                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC force removed modals with JavaScript\n")
                            except:
                                pass

                            # Method D: Final verification
                            time.sleep(0.5)
                            still_visible = False
                            for popup_text in popup_texts:
                                if self.__checkElement(By.XPATH, f'//div[contains(text(), "{popup_text}")]', 0.2):
                                    still_visible = True
                                    break

                            if not still_visible:
                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC successfully closed keyboard shortcuts popup\n")
                            else:
                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC popup still visible after all attempts\n")

                    except Exception as e:
                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] TTC popup closing error: {e}\n")

                    # Try to find and click comment input
                    comment_posted = False

                    # Method 1: Optimized TikTok comment selectors (2025) - prioritize most reliable selectors
                    selectors_to_try = [
                        # Most reliable TikTok comment selectors (updated for 2025)
                        'div[data-e2e="comment-input"] div[contenteditable="true"]',
                        'div[data-e2e="comment-input"] div[role="textbox"]',
                        'div[data-e2e="comment-input"]',
                        # New TikTok comment selectors
                        'div[data-e2e="comment-input-container"] div[contenteditable="true"]',
                        'div[data-e2e="comment-input-container"] div[role="textbox"]',
                        'div[data-e2e="comment-input-container"]',
                        # Generic but reliable selectors
                        'div[role="textbox"][contenteditable="true"]',
                        'div[contenteditable="true"][data-placeholder*="comment"]',
                        'div[contenteditable="true"][data-placeholder*="Comment"]',
                        'div[contenteditable="true"][placeholder*="comment"]',
                        'div[contenteditable="true"][placeholder*="Comment"]',
                        # Fallback selectors
                        'div[contenteditable="true"]',
                        '[contenteditable="true"]',
                        'textarea[placeholder*="comment"]',
                        'textarea[placeholder*="Comment"]',
                        'input[placeholder*="comment"]',
                        'input[placeholder*="Comment"]'
                    ]

                    for selector in selectors_to_try:
                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] TTC trying selector: {selector}\n")

                        if self.__checkElement(By.CSS_SELECTOR, selector, 0.5):  # Reduced timeout to 0.5 second for speed
                            try:
                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC found comment input with selector: {selector}\n")

                                # Close any popups first
                                self.__closePopups()

                                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Tìm thấy ô comment: {selector}',self.gui.tableWidget)
                                comment_input = self.driver.find_element(By.CSS_SELECTOR, selector)

                                # If this is a container element, try to find the actual input child
                                if selector == 'div[data-e2e="comment-input"]':
                                    try:
                                        # Try to find the actual input element inside
                                        child_selectors = [
                                            'div[contenteditable="true"]',
                                            'div[role="textbox"]',
                                            '[contenteditable="true"]'
                                        ]
                                        for child_sel in child_selectors:
                                            try:
                                                child_input = comment_input.find_element(By.CSS_SELECTOR, child_sel)
                                                comment_input = child_input
                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC found child input: {child_sel}\n")
                                                break
                                            except:
                                                continue
                                    except:
                                        pass

                                # Optimized click method with faster timing
                                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'auto', block: 'center'});", comment_input)
                                time.sleep(0.3)  # Reduced from 0.8s to 0.3s

                                # Scale-aware click method with enhanced fallbacks
                                click_success = False

                                # Check if browser is scaled down
                                scale_factor = self.settings.get('chromeScaleFactor', 1.0)
                                is_scaled = scale_factor < 0.9

                                if is_scaled:
                                    # For scaled browsers, prioritize JavaScript click
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC detected scaled browser ({scale_factor}), using JS-first approach\n")

                                    # Method 1: JavaScript click (most reliable for scaled browsers)
                                    try:
                                        self.driver.execute_script("arguments[0].click(); arguments[0].focus();", comment_input)
                                        click_success = True
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC comment input clicked with JavaScript (scaled)\n")
                                    except Exception as e:
                                        # Method 2: ActionChains with offset compensation
                                        try:
                                            # Get element center and adjust for scale
                                            location = comment_input.location
                                            size = comment_input.size
                                            center_x = location['x'] + size['width'] // 2
                                            center_y = location['y'] + size['height'] // 2

                                            ActionChains(self.driver).move_to_element_with_offset(comment_input, 0, 0).click().perform()
                                            click_success = True
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC comment input clicked with ActionChains (scaled compensated)\n")
                                        except Exception as e2:
                                            # Method 3: Direct click fallback
                                            try:
                                                comment_input.click()
                                                click_success = True
                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC comment input clicked directly (scaled fallback)\n")
                                            except Exception as e3:
                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC all scaled click methods failed: JS={e}, AC={e2}, Direct={e3}\n")
                                else:
                                    # For normal browsers, use standard approach
                                    # Method 1: ActionChains click (most reliable for normal TikTok)
                                    try:
                                        ActionChains(self.driver).move_to_element(comment_input).click().perform()
                                        click_success = True
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC comment input clicked with ActionChains (normal)\n")
                                    except Exception as e:
                                        # Method 2: JavaScript fallback
                                        try:
                                            self.driver.execute_script("arguments[0].click(); arguments[0].focus();", comment_input)
                                            click_success = True
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC comment input clicked with JavaScript (normal fallback)\n")
                                        except Exception as e2:
                                            # Method 3: Direct click
                                            try:
                                                comment_input.click()
                                                click_success = True
                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC comment input clicked directly (normal final)\n")
                                            except Exception as e3:
                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC all normal click methods failed: AC={e}, JS={e2}, Direct={e3}\n")

                                time.sleep(0.3)  # Reduced from 0.5s to 0.3s

                                # Clear any existing text thoroughly
                                try:
                                    # Multiple clear methods to ensure complete clearing
                                    comment_input.clear()
                                    time.sleep(0.2)

                                    # JavaScript clear for contenteditable elements
                                    self.driver.execute_script("""
                                        arguments[0].textContent = '';
                                        arguments[0].innerText = '';
                                        arguments[0].innerHTML = '';
                                        if (arguments[0].value !== undefined) arguments[0].value = '';
                                    """, comment_input)
                                    time.sleep(0.2)

                                    # Select all and delete as backup
                                    from selenium.webdriver.common.keys import Keys
                                    comment_input.send_keys(Keys.CONTROL + "a")
                                    time.sleep(0.1)
                                    comment_input.send_keys(Keys.DELETE)
                                    time.sleep(0.2)

                                except Exception as e:
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC clear failed: {e}\n")

                                time.sleep(0.3)

                                # Clean and validate comment text
                                def clean_comment_text(text):
                                    import re
                                    if not text or len(text.strip()) == 0:
                                        return "Nice video!"  # Default fallback

                                    # Remove emojis and other non-BMP characters
                                    # Keep Vietnamese characters and basic punctuation
                                    cleaned = re.sub(r'[^\u0000-\uFFFF]', '', text)  # Remove non-BMP characters
                                    # Replace common emojis with text equivalents
                                    emoji_replacements = {
                                        '💼': '',
                                        '🔥': '',
                                        '❤️': '♥',
                                        '😍': ':)',
                                        '😘': ':*',
                                        '👍': '',
                                        '💕': '♥',
                                        '💖': '♥',
                                        '🥰': ':)',
                                        '😊': ':)',
                                        '✨': '*',
                                        '💯': '100%'
                                    }
                                    for emoji, replacement in emoji_replacements.items():
                                        cleaned = cleaned.replace(emoji, replacement)

                                    cleaned = cleaned.strip()
                                    # Ensure we have valid text
                                    if len(cleaned) < 2:
                                        return "Nice video!"
                                    return cleaned

                                comment_text = clean_comment_text(comment_text)

                                # Debug log the actual comment text
                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC cleaned comment text: '{comment_text}' (length: {len(comment_text)})\n")

                                # Type comment text
                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC typing comment: {comment_text[:50]}...\n")

                                # Initialize comment_copied variable
                                comment_copied = False  # For now, always type directly

                                # Check if input already has content (avoid duplication)
                                existing_content = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()
                                if existing_content and len(existing_content) >= len(comment_text) * 0.8:
                                    typing_success = True
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC content already exists, skipping typing: '{existing_content[:30]}...'\n")
                                else:
                                    # Try multiple typing methods for better reliability
                                    typing_success = False

                                    # Method 1: Enhanced typing with focus and validation
                                    try:
                                        # Ensure element is focused first
                                        self.driver.execute_script("arguments[0].focus();", comment_input)
                                        time.sleep(0.2)

                                        # Clear any existing content first
                                        self.driver.execute_script("""
                                            arguments[0].textContent = '';
                                            arguments[0].innerText = '';
                                            arguments[0].innerHTML = '';
                                            if (arguments[0].value !== undefined) arguments[0].value = '';
                                        """, comment_input)
                                        time.sleep(0.2)

                                        # Type the comment text
                                        comment_input.send_keys(comment_text)
                                        time.sleep(0.5)  # Optimized wait time for TikTok to process

                                        # Verify content was entered
                                        current_value = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()

                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC typing method 1 result: expected='{comment_text}', actual='{current_value}'\n")

                                        if len(current_value) >= len(comment_text) * 0.8:  # 80% accuracy (more lenient)
                                            typing_success = True
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC typing method 1 success: {len(current_value)}/{len(comment_text)} chars\n")
                                        else:
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC typing method 1 insufficient content: {len(current_value)}/{len(comment_text)} chars\n")
                                    except Exception as e:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC typing method 1 failed: {e}\n")

                                # Method 2: Character by character with enhanced focus
                                if not typing_success:
                                    try:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC trying method 2: character by character\n")

                                        # Ensure focus and clear
                                        self.driver.execute_script("arguments[0].focus();", comment_input)
                                        time.sleep(0.2)
                                        self.driver.execute_script("arguments[0].textContent = ''; arguments[0].value = '';", comment_input)
                                        time.sleep(0.3)

                                        # Type character by character with enhanced timing
                                        for i, char in enumerate(comment_text):
                                            comment_input.send_keys(char)
                                            if i == 0:  # First character gets extra time
                                                time.sleep(0.3)
                                            elif i < 5:  # First few characters get more time
                                                time.sleep(0.1)
                                            else:
                                                time.sleep(0.05)

                                        time.sleep(1)  # Longer wait for processing
                                        current_value = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()

                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC method 2 result: expected='{comment_text}', actual='{current_value}'\n")

                                        if len(current_value) >= len(comment_text) * 0.7:  # More lenient
                                            typing_success = True
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC typing method 2 success: {len(current_value)}/{len(comment_text)} chars\n")
                                    except Exception as e:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC typing method 2 failed: {e}\n")

                                # Method 3: Enhanced JavaScript injection with events
                                if not typing_success:
                                    try:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC trying method 3: JavaScript injection\n")

                                        # Focus and clear first
                                        self.driver.execute_script("arguments[0].focus();", comment_input)
                                        time.sleep(0.2)

                                        # Set content with JavaScript
                                        self.driver.execute_script("""
                                            arguments[0].textContent = arguments[1];
                                            arguments[0].innerText = arguments[1];
                                            if (arguments[0].value !== undefined) {
                                                arguments[0].value = arguments[1];
                                            }
                                        """, comment_input, comment_text)

                                        # Trigger comprehensive events
                                        self.driver.execute_script("""
                                            var element = arguments[0];
                                            var events = ['input', 'change', 'keyup', 'keydown', 'focus'];
                                            events.forEach(function(eventType) {
                                                var event = new Event(eventType, { bubbles: true, cancelable: true });
                                                element.dispatchEvent(event);
                                            });
                                        """, comment_input)

                                        time.sleep(1)
                                        current_value = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()

                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC method 3 result: expected='{comment_text}', actual='{current_value}'\n")

                                        if len(current_value) >= len(comment_text) * 0.6:  # Even more lenient
                                            typing_success = True
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC typing method 3 success: {len(current_value)}/{len(comment_text)} chars\n")
                                        else:
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC typing method 3 insufficient content\n")
                                    except Exception as e:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC typing method 3 failed: {e}\n")

                                # Wait and verify content multiple times
                                for attempt in range(3):
                                    time.sleep(1)  # Wait for typing to complete
                                    current_value = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()

                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC verify attempt {attempt+1}: expected='{comment_text[:30]}...', actual='{current_value[:30]}...'\n")

                                    if current_value and len(current_value) >= len(comment_text) * 0.8:  # At least 80% of text
                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Comment verified: {len(current_value)}/{len(comment_text)} chars',self.gui.tableWidget)
                                        break
                                    else:
                                        # Only retry if this is not the last attempt
                                        if attempt < 2:  # Only retry on first 2 attempts
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC content incomplete, retrying...\n")

                                            # Thorough clear before retry
                                            self.driver.execute_script("""
                                                arguments[0].textContent = '';
                                                arguments[0].innerText = '';
                                                arguments[0].innerHTML = '';
                                                if (arguments[0].value !== undefined) arguments[0].value = '';
                                            """, comment_input)
                                            time.sleep(0.3)

                                            # Enhanced retry with JavaScript and events
                                            self.driver.execute_script("""
                                                arguments[0].textContent = arguments[1];
                                                arguments[0].innerText = arguments[1];
                                                if (arguments[0].value !== undefined) arguments[0].value = arguments[1];

                                                // Trigger events to ensure TikTok detects the change
                                                var events = ['input', 'change', 'keyup', 'focus'];
                                                events.forEach(function(eventType) {
                                                    var event = new Event(eventType, { bubbles: true });
                                                    arguments[0].dispatchEvent(event);
                                                });
                                            """, comment_input, comment_text)
                                        else:
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC max retry attempts reached, accepting current content\n")
                                            break

                                # Method 4: Clipboard fallback if all typing methods failed
                                if not typing_success:
                                    try:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC trying method 4: clipboard fallback\n")

                                        # Try using clipboard as last resort
                                        import pyperclip
                                        pyperclip.copy(comment_text)
                                        time.sleep(0.2)

                                        # Focus and clear
                                        self.driver.execute_script("arguments[0].focus();", comment_input)
                                        time.sleep(0.2)
                                        self.driver.execute_script("arguments[0].textContent = ''; arguments[0].value = '';", comment_input)
                                        time.sleep(0.2)

                                        # Paste from clipboard
                                        from selenium.webdriver.common.keys import Keys
                                        comment_input.send_keys(Keys.CONTROL + "v")
                                        time.sleep(1)

                                        current_value = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()

                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC method 4 result: expected='{comment_text}', actual='{current_value}'\n")

                                        if len(current_value) >= len(comment_text) * 0.5:  # Very lenient
                                            typing_success = True
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC typing method 4 success: {len(current_value)}/{len(comment_text)} chars\n")
                                    except Exception as e:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC typing method 4 failed: {e}\n")

                                time.sleep(0.5)

                                # Verify text was actually entered with multiple checks
                                try:
                                    current_value = comment_input.get_attribute('value') or comment_input.text or self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText;", comment_input)
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC comment input current value: '{current_value[:50]}...'\n")

                                    if current_value and len(current_value.strip()) > 0:
                                        comment_typed = True
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC comment text verified in input\n")
                                    else:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC comment text NOT found in input, trying next selector\n")
                                        continue
                                except Exception as e:
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC comment verification failed: {e}\n")
                                    continue

                                # Final verification before posting - check for missing first character
                                final_content = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()

                                # Check if first character is missing and fix it (only for longer comments)
                                if final_content and comment_text and len(comment_text) > 3:
                                    expected_first_char = comment_text[0].lower()
                                    actual_first_char = final_content[0].lower() if final_content else ''

                                    if expected_first_char != actual_first_char and len(final_content) > len(comment_text) * 0.7:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC detected missing first character, fixing...\n")

                                        # Use JavaScript to fix without duplication risk
                                        self.driver.execute_script("""
                                            arguments[0].textContent = '';
                                            arguments[0].innerText = '';
                                            arguments[0].innerHTML = '';
                                            if (arguments[0].value !== undefined) arguments[0].value = '';
                                        """, comment_input)
                                        time.sleep(0.3)

                                        # Set content with JavaScript (no duplication risk)
                                        self.driver.execute_script("arguments[0].textContent = arguments[1];", comment_input, comment_text)
                                        time.sleep(0.5)

                                        # Re-verify
                                        final_content = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()

                                if not final_content or len(final_content) < 3:
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC final verification failed, content too short: '{final_content}'\n")
                                    continue  # Try next selector

                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC final verification passed: '{final_content[:50]}...'\n")

                                # Try to find and click post button - OPTIMIZED SEARCH
                                time.sleep(0.5)  # Optimized wait for post button to appear

                                # Optimized post button selectors (updated for 2025)
                                post_selectors = [
                                    # Primary TikTok post button selectors
                                    '[data-e2e="comment-post"]',
                                    'button[data-e2e="comment-post"]',
                                    'div[data-e2e="comment-post"]',
                                    # Alternative selectors for different TikTok versions
                                    '[data-e2e="comment-submit"]',
                                    'button[data-e2e="comment-submit"]',
                                    '[data-e2e="comment-send"]',
                                    'button[data-e2e="comment-send"]',
                                    # Generic post button selectors
                                    'button[aria-label*="Post"]',
                                    'button[aria-label*="Send"]',
                                    'button[aria-label*="Đăng"]',
                                    'button[aria-label*="Gửi"]',
                                    # CSS class-based selectors
                                    'button[class*="comment-post"]',
                                    'button[class*="comment-submit"]',
                                    'button[class*="post-btn"]',
                                    'button[class*="send-btn"]'
                                ]

                                post_clicked = False
                                for post_selector in post_selectors:
                                    if self.__checkElement(By.CSS_SELECTOR, post_selector, 0.5):  # Faster check
                                        try:
                                            post_btn = self.driver.find_element(By.CSS_SELECTOR, post_selector)
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC found post button: {post_selector}\n")

                                            # Enhanced click method with multiple approaches
                                            click_methods = [
                                                # Method 1: ActionChains (most reliable for modern TikTok)
                                                lambda: ActionChains(self.driver).move_to_element(post_btn).click().perform(),
                                                # Method 2: JavaScript click
                                                lambda: self.driver.execute_script("arguments[0].click();", post_btn),
                                                # Method 3: Direct click
                                                lambda: post_btn.click()
                                            ]

                                            for i, click_method in enumerate(click_methods):
                                                try:
                                                    click_method()
                                                    post_clicked = True
                                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                        f.write(f"[{datetime.now()}] TTC post button clicked with method {i+1}: {post_selector}\n")
                                                    break
                                                except Exception as e:
                                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                        f.write(f"[{datetime.now()}] TTC click method {i+1} failed: {e}\n")
                                                    continue

                                            if post_clicked:
                                                break

                                        except Exception as e:
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC post button selector {post_selector} failed: {e}\n")
                                            continue

                                # Post-click verification and cleanup (only if post was clicked)
                                if post_clicked:
                                    # Wait and verify comment was actually posted
                                    time.sleep(1.5)  # Optimized wait for TikTok to process

                                    # Try to verify comment appeared (optional - don't fail if can't verify)
                                    try:
                                        # Look for our comment in the comment list
                                        comment_elements = self.driver.find_elements(By.CSS_SELECTOR, '[data-e2e="comment-level-1"], .comment-item, [class*="comment"]')
                                        comment_found = False
                                        for elem in comment_elements[-3:]:  # Check last 3 comments
                                            elem_text = elem.text.strip()
                                            if elem_text and len(elem_text) > 5:
                                                # Check if our comment text appears in recent comments
                                                if any(word in elem_text for word in final_content.split()[:3]):
                                                    comment_found = True
                                                    break

                                        if comment_found:
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC comment verified in comment list\n")
                                        else:
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC comment not found in list (may still be processing)\n")
                                    except Exception as e:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC comment verification failed: {e}\n")

                                    comment_posted = True
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC comment posted successfully\n")
                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Comment đã được đăng!',self.gui.tableWidget)
                                    break

                                # Quick XPath fallback if CSS fails
                                if not comment_posted:
                                    try:
                                        # Try the most common XPath (fast)
                                        post_btn = self.driver.find_element(By.XPATH, '//button[contains(text(), "Post") or contains(text(), "Đăng")]')
                                        self.driver.execute_script("arguments[0].click();", post_btn)
                                        comment_posted = True
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC comment posted with XPath\n")
                                    except:
                                        pass

                                # Last resort: Press Enter (fast)
                                if not comment_posted:
                                    try:
                                        from selenium.webdriver.common.keys import Keys
                                        comment_input.send_keys(Keys.ENTER)
                                        comment_posted = True
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC comment posted with ENTER key\n")
                                    except Exception as e:
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC ENTER key failed: {e}\n")

                                # Break after successful comment posting or typing
                                if comment_posted or comment_typed:
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC breaking selector loop - posted: {comment_posted}, typed: {comment_typed}\n")
                                    break

                            except Exception as e:
                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                    f.write(f"[{datetime.now()}] TTC selector {selector} failed: {e}\n")
                                continue

                    # Method 2: Enhanced XPath fallback for comment input if CSS failed
                    if not comment_posted:
                        xpath_inputs = [
                            # More specific XPath selectors for TikTok 2025
                            '//div[@data-e2e="comment-input"]//div[@contenteditable="true"]',
                            '//div[@data-e2e="comment-input-container"]//div[@contenteditable="true"]',
                            '//div[@contenteditable="true" and contains(@data-placeholder, "comment")]',
                            '//div[@contenteditable="true" and contains(@placeholder, "comment")]',
                            '//div[@contenteditable="true" and contains(@data-placeholder, "Comment")]',
                            '//div[@contenteditable="true" and contains(@placeholder, "Comment")]',
                            '//div[@contenteditable="true" and contains(@aria-label, "comment")]',
                            '//div[@contenteditable="true" and contains(@aria-label, "Comment")]',
                            '//textarea[contains(@placeholder, "comment")]',
                            '//textarea[contains(@placeholder, "Comment")]',
                            '//input[contains(@placeholder, "comment")]',
                            '//input[contains(@placeholder, "Comment")]',
                            '//div[@contenteditable="true"]'
                        ]

                        for xpath in xpath_inputs:
                            if self.__checkElement(By.XPATH, xpath, 2):  # Slightly longer timeout for XPath
                                try:
                                    comment_input = self.driver.find_element(By.XPATH, xpath)
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC found comment input with XPath: {xpath}\n")

                                    # Enhanced click method for XPath
                                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", comment_input)
                                    time.sleep(0.8)

                                    # Try multiple click methods
                                    click_success = False
                                    try:
                                        ActionChains(self.driver).move_to_element(comment_input).click().perform()
                                        click_success = True
                                    except:
                                        try:
                                            comment_input.click()
                                            click_success = True
                                        except:
                                            self.driver.execute_script("arguments[0].click(); arguments[0].focus();", comment_input)
                                            click_success = True

                                    if click_success:
                                        time.sleep(0.5)

                                        # Clear and type comment
                                        try:
                                            comment_input.clear()
                                            self.driver.execute_script("arguments[0].textContent = ''; arguments[0].value = '';", comment_input)
                                        except:
                                            pass

                                        time.sleep(0.3)

                                        # Type comment text
                                        try:
                                            comment_input.send_keys(comment_text)
                                            time.sleep(1)

                                            # Verify text was entered
                                            current_value = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText || arguments[0].value || '';", comment_input).strip()
                                            if current_value and len(current_value) >= len(comment_text) * 0.7:
                                                # Try Enter key as post method
                                                from selenium.webdriver.common.keys import Keys
                                                comment_input.send_keys(Keys.ENTER)
                                                comment_posted = True
                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC comment posted with XPath and ENTER\n")
                                                break
                                        except Exception as e:
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC XPath typing failed: {e}\n")
                                            continue
                                except Exception as e:
                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC XPath {xpath} failed: {e}\n")
                                    continue

                    if comment_posted:
                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Comment "{comment_text[:20]}..." thành công!',self.gui.tableWidget)
                        if self.settings['bypassCaptcha'] == False:self.bypassCaptcha(3)
                        self.__jobLap.append(self.__link)
                        return True
                    else:
                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                            f.write(f"[{datetime.now()}] TTC no comment input found with any selector\n")
                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Không tìm thấy ô comment!!!',self.gui.tableWidget)
                        return False

                except Exception as e:
                    self.__handle_exceptions('Error Comment Video {}'.format(self.__link))
                    return False
        else:
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Chuyển hương tới {self.__link} thất bại!!!',self.gui.tableWidget) ; return False  
   
    def __nextAccount(self):
        for t in range(2):
            if 'Block-Media' in self.extension['ExtensionChecked']:self.__blockImage('BLOCK_ON')
            # kiểm tra thư mục đã có tài khoản hay chưa nếu rồi lấy tk hoặc mk trong thư mục đó load lên table 
            try:
                try:self.__deleteAccounts()
                except:pass
                self.dataAccTikTok = ''
                self.dataAccTikTok = next(self.gui.iterDataTikTok)
                self.iterAcc = True
                if '|' in self.dataAccTikTok:
                    self.userTikTok, self.passTikTok, self.cookieTik = self.dataAccTikTok.split('|')[0], self.dataAccTikTok.split('|')[1], ''
                elif self.dataAccTikTok != '':
                    self.cookieTik, self.userTikTok, self.passTikTok = self.dataAccTikTok, '', ''
                else:
                    self.cookieTik, self.userTikTok, self.passTikTok = '', '', ''
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra cookie thành công !',self.gui.tableWidget )
                self.editCellByColumnName.emit(self.index, 'UID', str(self.userTikTok),self.gui.tableWidget)
                self.editCellByColumnName.emit(self.index, 'Password', str(self.passTikTok),self.gui.tableWidget)
                self.editCellByColumnName.emit(self.index, 'Cookie', str(self.cookieTik),self.gui.tableWidget)
                self.__deleteCookie();self.status = 'Tòn đẹp trai'
            except:self.dataAccTikTok = ''
            if self.iterAcc == True and self.dataAccTikTok == '':self.status = 'Đã sử dụng hết tài khoản TikTok!!!'; return False
            # Kiểm tra đăng nhập cookie or html data
            self.__checkInternet()
            for t in range(3):
                if self.settings['Browser'] == 'GoLogin':self.__deleteCookie()
                if self.__checkCookie():
                    with open(f'{pathOutput}\\cookies.txt','a+',encoding='utf-8') as f:
                        f.write(f'{self.cookieChrome}\n')
                    return True
                else:self.__deleteCookie()
                if len(self.userTikTok) >= 5 and len(self.passTikTok) >= 5:
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đăng nhập tài khoản TikTok [ {self.userTikTok} ]',self.gui.tableWidget)
                    self.loginTikTok();time.sleep(3)
                    if self.__checkCookie():self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đăng nhập tài khoản TikTok [ {self.userTikTok} ] thành công.',self.gui.tableWidget);return True
                elif len(self.cookieTik) >= 30:
                    self.__addCookie();time.sleep(3)
                    if self.__checkCookie():self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đăng nhập tài khoản TikTok [ {self.userTikTok} ] thành công.',self.gui.tableWidget);return True
                else:
                    self.status = 'Đã sử dụng hết tài khoản TikTok!!!'
                    return False
                    # self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra đăng nhập lần tới sau 60s.',self.gui.tableWidget);time.sleep(60)
                if 'Mật khẩu sai'  in self.status or 'Sai tài khoản hoặc mật khẩu' in self.status or 'Tài khoản của bạn đã bị đình chỉ.'  in self.status or 'Mật khẩu của bạn đã hết hạn và phải được thay đổi để giữ an toàn cho tài khoản.' in self.status:
                    return False

    def __earnMoneyTDS(self):
        while True:
            try:
                def sigInTDS():
                    self.__updateValue()
                    try:
                        for t in range(3):
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Login TDS [ {} ] Proxy: {}'.format(self.userCoin, self.proxyRequests),self.gui.tableWidget )
                            self.infoCoin = self.LoginTDS()
                            if self.infoCoin['status'] == 'success':
                                return True
                            time.sleep(random.randint(5,15))
                        if self.infoCoin['status'] == 'error':
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.infoCoin['mess']),self.gui.tableWidget);return False
                    except:pass
                    return False
                
                if self.__nextAccount():
                    if sigInTDS():
                        if self.__activeTokenChrome():
                            self.delaySettings('activated')
                            while (True):
                                try:
                                    self.settings = json.loads(open(f'{pathConfig}\\settings.json', 'r', encoding="utf-8-sig").read())
                                    def getXuTDS():
                                        self.delaySettings('getXu')
                                        getXu = self.__traodoisub.getXuJob(self.__typeJob)
                                        if getXu['status'] == 'success':
                                            self.statusBar.emit('coinR', int(getXu['mess']))
                                            self.__result = int(getXu['mess']) + self.__result
                                            self.editCellByColumnName.emit(self.index, 'XuThem', str(self.__result),self.gui.tableWidget)
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Nhận thành công {getXu["mess"]} Xu Tổng xu nhận được là: {self.__result} Xu',self.gui.tableWidget)
                                            # - - - - - - - - - - Tính Rate Job - - - - - - - - - - - - -
                                            if self.__typeJob == 'follow': maxjob = self.settings['cacheFollow']; coin = self.followCoinTDS
                                            else: maxjob = self.settings['cacheLove']; coin = self.loveCoinTDS
                                            self.__rate = int((getXu['mess'] / (maxjob * coin)) * 100)
                                            self.editCellByColumnName.emit(self.index, 'Tỷ lệ', f'{getXu["mess"]} / {self.__rate}%',self.gui.tableWidget)
                                            self.__print(f'ToTal {self.__result} Rate {getXu["mess"]} / {self.__rate}%')
                                            if self.__rate <= self.settings['rateNext']:self.__rateJobs +=1
                                            self.__faildGetXu = 0
                                            # - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
                                            
                                            if int(getXu['mess']) == 0:
                                                self.__faildGetXu +=1;self.__faild = 10;self.status = f"Bạn chưa {self.__typeJob} nick nào, hãy {self.__typeJob} trước khi nhận xu Can't get coins: {self.__faildGetXu}"
                                        else:
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(getXu['mess']),self.gui.tableWidget)
                                            if getXu['mess'] == 'Vui lòng công khai danh sách video đã thích trên tài khoản tiktok rồi quay lại nhận!':
                                                self.apiTikTok  = TikTok_Api(self.cookieChrome)
                                                if self.settings['publicLove']:self.apiTikTok.publicLoveAPI()
                                                self.__publicLove += 1
                                                if self.__publicLove >= 3:
                                                    self.__faildGetXu = self.settings['nextAccFaild']+1;self.__faild = 10;self.status = 'Công khai tim thất bại 3 lần dừng.'
                                                    
                                        time.sleep(4)
                                    # - - - - - - - - - - Chuyển tài khoản TIKTOK - - - - - - - - - - - - -
                                    def runNext():
                                        self.userTikTok,self.passTikTok,self.cookieTik = '', '', '';self.__deleteCookie();self.__restoreData();
                                        if self.__nextAccount():
                                            self.__activeTokenChrome(); self.delaySettings('config');return True
                                        else:
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(1)
                                        return False

                                    self.__typeJob = self.settings['Task'].lower()
                                    
                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Tiến hành lấy nhiệm vụ TDS',self.gui.tableWidget)
                                    getjob = self.__traodoisub.getJob(self.__typeJob)
                
                                    if 'Có lẽ tài khoản của bạn đã bị chặn tương tác, bạn đã làm thất bại quá nhiều lần!' in str(getjob):
                                        if self.dataAccTikTok == '':self.status = 'Có lẽ tài khoản của bạn đã bị chặn tương tác, bạn đã làm thất bại quá nhiều lần!';return False
                                        self.status = getjob['error']
                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget)
                                        time.sleep(3)
                                        if runNext() == False:return
                                        break
                                    
                                    if 'countdown' in str(getjob) or "Vui lòng thao tác chậm lại" in str(getjob):
                                        for t in range(int(getjob['countdown']),0,-1):
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'{getjob["error"]} countdown: {t}/{getjob["countdown"]}',self.gui.tableWidget)
                                            time.sleep(2)
                                    if 'data' not in str(getjob) or 'countdown' in str(getjob) or "Vui lòng thao tác chậm lại" in str(getjob):
                                        for i in range(int(random.randint(self.settings['delayWaitJob1'],self.settings['delayWaitJob2'])),0,-1):
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Hết nhiệm vụ {self.__typeJob.upper()} tạm nghỉ sau {i} giây ',self.gui.tableWidget)
                                            time.sleep(1)
                                        sigInTDS()
                                        continue
                                        
                                    if getjob != 0 and 'countdown' in str(getjob) or "Vui lòng thao tác chậm lại" in str(getjob): break
                                    for demJob, job in zip(range(11),getjob['data']):
                                        self.__dem += 1
                                        self.countJob = len(getjob['data'])
                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Tìm thấy {len(getjob["data"])} nhiệm vụ {self.__typeJob.upper()}',self.gui.tableWidget)
                                        if str(job['link']) in str(self.__jobDie):
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Video không khả dụng [ {} ] bỏ qua!'.format(job["link"]),self.gui.tableWidget)
                                        elif str(job['link']) in str(self.__jobLap):
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Nhiệm vụ này đã được làm rồi [ {} ] bỏ qua!'.format(job["link"]),self.gui.tableWidget)
                                            time.sleep(3)
                                        else:
                                            self.__demJob = demJob
                                            idpost, self.__link = job['id'], job['link']
                                            guiduyet = self.__traodoisub.postCache(self.__typeJob,idpost)
                                            self.__textCache = guiduyet
                                            if guiduyet['msg'] == 'Nhiệm vụ này đã hoàn thành vui lòng thử nhiệm vụ khác':continue
                                            # if self.dataAccTikTok == '':
                                            if 'Có lẽ tài khoản của bạn đã bị chặn tương tác, bạn đã làm thất bại quá nhiều lần!' in str(self.__textCache):
                                                self.status = 'Có lẽ tài khoản của bạn đã bị chặn tương tác, bạn đã làm thất bại quá nhiều lần!'
                                                return False
                                            self.__print(f'ID POST: {idpost} | {self.__textCache}')
                                            if self.__typeJob == 'follow':
                                                if self.__textCache['cache'] > self.__cache or self.__textCache['cache'] < self.__cache:self.__sendTask = True
                                                else:sigInTDS();self.editCellByColumnName.emit(self.index, 'Trạng Thái', f"{self.__textCache['msg']}",self.gui.tableWidget);time.sleep(3);self.__sendTask = False
                                            else:self.__sendTask = True
                                            self.status = f'Gửi duyệt nhiệm vụ {self.__typeJob} thành công.'
                                            self.__cache = guiduyet['cache']
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget)
                                            if self.__sendTask == True:
                                                self.__print(f'ToTal: {self.__dem} TaskId: {idpost} Link: {self.__link}')
                                                try:
                                                    if self.settings['methodFollow'] != 'FOLLOW SEARCH' or self.__typeJob == 'love':self.driver.get(self.__link)
                                                    else: self.driver.get(f'{self.url_tiktok}@{self.__link}')
                                                except:pass

                                                
                                                self.__clickXmlJobs()
                                                self.__total +=1; self.__error = 0
                                                self.statusBar.emit('jobs', 0)
                                                
                                                
                                                self.editCellByColumnName.emit(self.index, 'Job/Cache', f'{self.__dem}/{self.__cache}',self.gui.tableWidget)
                                                
                                                if (self.__typeJob == 'follow' and self.__cache >= int(self.settings['cacheFollow'])) or (self.__typeJob == 'love' and self.__cache >= int(self.settings['cacheLove'])):
                                                    getXuTDS();self.__cache = 0
                                                    self.xuHT = self.__traodoisub.getXuTDS()
                                                    self.editCellByColumnName.emit(self.index, 'XuHT', str(self.xuHT['xu']),self.gui.tableWidget)
                                                    
                                                if self.__rateJobs >= self.settings['nextAccFaild']:
                                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
                                                    self.__rateJobs = 0
                                                    if self.settings['reopenChrome'] and self.__reopenChrome < 3: 
                                                        self.__reopenChrome +=1
                                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Khởi động lại Trình Duyệt...',self.gui.tableWidget);time.sleep(3)
                                                        try:
                                                            self.driver.quit();self.__gologin.stop()
                                                        except:pass 
                                                        if self.setupChrome():
                                                            self.__addCookie();time.sleep(3)
                                                            if self.__checkCookie():self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đăng nhập tài khoản TikTok [ {self.userTikTok} ] thành công.',self.gui.tableWidget);break
                                                            self.status = f'Đăng nhập tài khoản TikTok [ {self.userTikTok} ] thất bại.'
                                                            return
                                                        else:return
                                                    else:
                                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
                                                        self.userTikTok,self.passTikTok,self.cookieTik = '', '', '';self.__reopenChrome = 0;self.__deleteCookie();self.__restoreData();
                                                        if runNext() == False:return
                                                if self.__typeJob == 'follow':
                                                    if self.__dem >= self.settings['maxJobFollow']:
                                                        self.status = f'Đã làm đủ {self.settings["maxJobLove"]} nhiệm vụ {self.__typeJob}.'
                                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
                                                        if runNext() == False:return
                                                
                                                if self.__typeJob == 'love':
                                                    if self.__dem >= self.settings['maxJobLove']:
                                                        self.status = f'Đã làm đủ {self.settings["maxJobLove"]} nhiệm vụ {self.__typeJob}.'
                                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
                                                        if runNext() == False:return
                                                self.delaySettings('getJob')
                                                    
                                except Exception as e:
                                    self.__handle_exceptions('Error While TDS Start')
                                    self.delaySettings('error')
                                    self.__checkInternet()            
                                    sigInTDS()
                                    self.__error +=1
                                    if self.__error >=10:self.__error=0;self.status = f'Lỗi liên tục [ {self.__error} ] lần, bắt đầu lại!!!' ;break
                        else:
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(3)
                            if 'Tài khoản tiktok này đang làm nhiệm vụ ở một tài khoản khác, vui lòng dừng mọi thao tác ở tài khoản' in self.status and self.iterAcc == False:
                                self.status = 'Đã sử dụng hết tài khoản TikTok - Tài khoản tiktok này đang làm nhiệm vụ ở một tài khoản khác'
                                break
                else:
                    if self.dataAccTikTok == '':
                        if 'Mật khẩu sai' in self.status or 'Sai tài khoản hoặc mật khẩu' in self.status or 'Tài khoản của bạn đã bị đình chỉ.'  in self.status and self.iterAcc == False:
                            return False
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(3)
            except Exception as e:
                self.__handle_exceptions(e);
                try:getXuTDS()
                except:pass
                self.delaySettings('error')
                self.__checkInternet()

    
    def __earnMoneyTTC(self):
        while True:
            try:
                self.settings = json.loads(open(f'{pathConfig}\\settings.json', 'r', encoding="utf-8-sig").read())
                self.__updateValue()
                def sigInTTC():
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Login TTC [ {} ] Proxy: {}'.format(self.userCoin, self.proxyRequests),self.gui.tableWidget )
                    for t in range(3):
                        self.info = self.LoginTTC()
                        if self.info['status'] == 'success':
                            return True
                        time.sleep(random.randint(3,6))
                    return False
                    
                if sigInTTC():
                    
                    def addTikTok():
                        for t in range(10):
                            
                            self.datnik = self.__tuongtaccheo.datNik(self.infoTikApi["uniqueId"])
                            if self.datnik['status'] == 'error':
                                self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.datnik['mess']),self.gui.tableWidget);time.sleep(5)
                            else:
                                self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.datnik['mess']),self.gui.tableWidget);break
                    
                    if self.__nextAccount():
                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đặt Nick [ {self.infoTikApi["uniqueId"]} ]',self.gui.tableWidget )
                        addTikTok()
                        self.delaySettings('activated')
                        while (True):
                            while (True):
                                try:
                                    self.__typeJob = self.settings['Task'].lower();self.__updateValue()
                                    def getXuTik():
                            
                                        self.delaySettings('getXu')

                                        # Debug log for getXuJob
                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đang nhận xu cho {self.__typeJob} jobs: {self.__lstIDTTC.rstrip(",")}',self.gui.tableWidget)
                                        getXu = self.__tuongtaccheo.getXuJob(self.__typeJob,self.__lstIDTTC.rstrip(','))

                                        # Debug log response
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC getXuJob response: {getXu}\n")

                                        if getXu and getXu.get('status') == 'success':
                                            
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Nhận Thành Công {getXu["mess"]} Xu',self.gui.tableWidget)
                                            self.__print(getXu['mess'])
                                            self.statusBar.emit('coinR', int(getXu['mess']))
                                            self.__result = int(getXu['mess']) + self.__result
                                            self.editCellByColumnName.emit(self.index, 'XuThem', str(self.__result),self.gui.tableWidget)
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Nhận thành công {getXu["mess"]} Xu Tổng xu nhận được là: {self.__result} Xu',self.gui.tableWidget)
                                            
                                            # - - - - - - - - - - Tính Rate Job - - - - - - - - - - - - -
                                            if self.__typeJob == 'follow': maxjob = self.settings['cacheFollow']; coin = self.followCoinTTC
                                            else: maxjob = self.settings['cacheLove']; coin = self.loveCoinTTC
                                            self.__rate = int((getXu['mess'])) / (maxjob * coin) * 100
                                            self.__rate = int(self.__rate)
                                            self.editCellByColumnName.emit(self.index, 'Tỷ lệ', f'{getXu["mess"]} / {self.__rate}%',self.gui.tableWidget)
                                            # - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
                                            if self.__rate <= self.settings['rateNext']:self.__rateJobs +=1
                                            self.__lstIDTTC = ''; self.__cache = 0
                            
                                        else:
                                            error_msg = getXu.get('mess', 'Unknown error') if getXu else 'No response from getXuJob'
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Nhận xu thất bại: {error_msg}',self.gui.tableWidget)
                                            with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                f.write(f"[{datetime.now()}] TTC getXuJob failed: {getXu}\n")
                                            if getXu['mess'] == 'Vui lòng công khai danh sách video đã thích trên tài khoản tiktok rồi quay lại nhận!':
                                                self.apiTikTok  = TikTok_Api(self.cookieChrome)
                                                if self.settings['publicLove']:self.apiTikTok.publicLoveAPI()
                                                self.__publicLove += 1
                                                if self.__publicLove >= 3:
                                                    self.__faildGetXu = self.settings['nextAccFaild']+1;self.__faild = 10;self.status = 'Công khai tim thất bại 3 lần dừng.'
                                                    
                                            if getXu['mess'] == 'Bạn chưa like video nào, hãy like video trước khi nhận xu' or getXu['mess'] == 'Bạn chưa theo dõi nick nào, hãy theo dõi trước khi nhận xu':
                                                self.__faildGetXu +=1;self.__faild = 10;self.status = f"Bạn chưa {self.__typeJob} nick nào, hãy {self.__typeJob} trước khi nhận xu Can't get coins: {self.__faildGetXu}"
                                                self.editCellByColumnName.emit(self.index, 'Tỷ lệ', f'0 / 0%',self.gui.tableWidget)
                                                self.__rateJobs +=1
                                                
                                            else:self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(getXu['mess']),self.gui.tableWidget)
                                        if getXu['mess'] != 'Vui lòng công khai danh sách video đã thích trên tài khoản tiktok rồi quay lại nhận!':
                                            self.__idDone = ''
                                            self.__lstIDTTC = ''

                                    self.settings = json.loads(open(f'{pathConfig}\\settings.json', 'r', encoding="utf-8-sig").read())                                

                                    def runNext():
                                        self.userTikTok,self.passTikTok,self.cookieTik = '', '', '';self.__deleteCookie();self.__restoreData();
                                        if self.__nextAccount():
                                            self.__activeTokenChrome(); self.delaySettings('config');return True
                                        else:
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(1)
                                        return False
                    
                                    for t in range(10):
                                        getjob = self.__tuongtaccheo.getJob(self.__typeJob)
                                        self.__print(getjob)

                                        # Check for countdown/rate limit from TTC API
                                        if isinstance(getjob, dict) and ('countdown' in str(getjob) or 'error' in str(getjob)):
                                            if 'countdown' in str(getjob):
                                                countdown_time = getjob.get('countdown', 60)  # Default 60s if not specified
                                                error_msg = getjob.get('error', 'Rate limited')

                                                # Show initial countdown message
                                                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'⏳ {error_msg} - Đếm ngược {countdown_time}s',self.gui.tableWidget)

                                                for countdown in range(int(countdown_time), 0, -1):
                                                    # Calculate minutes and seconds for better display
                                                    minutes = countdown // 60
                                                    seconds = countdown % 60
                                                    if minutes > 0:
                                                        time_display = f'{minutes}m{seconds:02d}s'
                                                    else:
                                                        time_display = f'{seconds}s'

                                                    # Progress bar effect
                                                    progress = int((countdown_time - countdown) / countdown_time * 20)
                                                    bar = '█' * progress + '░' * (20 - progress)

                                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'⏳ Chờ {time_display} [{bar}] {countdown}/{countdown_time}',self.gui.tableWidget)
                                                    time.sleep(1)

                                                self.editCellByColumnName.emit(self.index, 'Trạng Thái', '✅ Đếm ngược hoàn tất, tiếp tục...',self.gui.tableWidget)
                                            continue  # Try again after countdown

                                        # Check if getjob is valid (not None, not 0, not error dict, and is iterable)
                                        if getjob and getjob != 0 and isinstance(getjob, (list, tuple)) and not isinstance(getjob, dict):
                                            break
                                        for i in range(int(random.randint(self.settings['delayWaitJob1'],self.settings['delayWaitJob2'])),0,-1):
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {t} ] Hết nhiệm vụ {self.__typeJob.upper()} tạm nghỉ sau {i} giây',self.gui.tableWidget)
                                            time.sleep(1)

                                    # Double check before iterating
                                    if not getjob or getjob == 0 or not isinstance(getjob, (list, tuple)) or isinstance(getjob, dict):
                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                            f.write(f"[{datetime.now()}] TTC jobs iteration skipped: getjob={type(getjob)}, value={getjob}\n")
                                        break

                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC starting jobs iteration: {len(getjob)} jobs\n")

                                    # Use comment settings for max jobs
                                    max_comment_jobs = self.settings.get('maxJobComment', 10)
                                    cache_comment_jobs = self.settings.get('cacheComment', 1)

                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                        f.write(f"[{datetime.now()}] TTC comment settings: max={max_comment_jobs}, cache={cache_comment_jobs}\n")

                                    for demJob, job in zip(range(max_comment_jobs),getjob):
                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Tìm thấy {len(getjob)} nhiệm vụ {self.__typeJob.upper()}',self.gui.tableWidget)
                                        self.countJob = len(getjob)
                                        if str(job['link']) in str(self.__jobDie):
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Video không khả dụng [ {} ] bỏ qua!'.format(job["link"]),self.gui.tableWidget);time.sleep(1)
                                        elif str(job['link']) in str(self.__jobLap):
                                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Nhiệm vụ này đã được làm rồi [ {} ] bỏ qua!'.format(job["link"]),self.gui.tableWidget);time.sleep(1)
                                        else:
                                            try:
                                                self.__demJob = demJob
                                                self.__dem += 1
                                                idpost, self.__link = job['idpost'], job['link']

                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC processing job {demJob+1}: {idpost}, {self.__link}\n")

                                                # For comment jobs, store comment content in job-specific variable
                                                if (self.__typeJob == 'cmt' or self.__typeJob == 'comment') and 'nd' in job:
                                                    current_job_comment = job['nd']
                                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                        f.write(f"[{datetime.now()}] TTC comment content found: {current_job_comment[:100]}...\n")
                                                else:
                                                    current_job_comment = None
                                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                        f.write(f"[{datetime.now()}] TTC no comment content for job type: {self.__typeJob}\n")

                                                if 'www' not in self.__link:self.__link = f'{self.url_tiktok}@{self.__link}'
                                                self.__print(f'url -> {self.__link}')
                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC navigating to: {self.__link}\n")

                                                try:
                                                    self.driver.get(self.__link)
                                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                        f.write(f"[{datetime.now()}] TTC navigation successful\n")
                                                except Exception as e:
                                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                        f.write(f"[{datetime.now()}] TTC navigation failed: {e}\n")
                                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Chuyển hướng tới {self.__link} thất bại!!!',self.gui.tableWidget)
                                                    time.sleep(random.randint(3,6))

                                                self.statusBar.emit('jobs',0)

                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC calling __clickXmlJobs with comment: {current_job_comment is not None}\n")

                                                click_result = self.__clickXmlJobs(current_job_comment)

                                                with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                    f.write(f"[{datetime.now()}] TTC __clickXmlJobs result: {click_result}\n")
                                                self.__total +=1
                                                self.__lstIDTTC += idpost + ',' ; self.__cache = len(self.__lstIDTTC.split(','))-1

                                                # Determine cache limit for current job type
                                                if self.__typeJob == 'follow':
                                                    cache_limit = self.settings['cacheFollow']
                                                elif self.__typeJob == 'love':
                                                    cache_limit = self.settings['cacheLove']
                                                elif self.__typeJob == 'cmt' or self.__typeJob == 'comment':
                                                    cache_limit = cache_comment_jobs
                                                else:
                                                    cache_limit = 10  # default

                                                # Update Job/Cache display immediately after each job
                                                self.editCellByColumnName.emit(self.index, 'Job/Cache', f'{self.__cache}/{cache_limit} (Total: {self.__total})',self.gui.tableWidget)

                                                # Update status bar with job count
                                                self.statusBar.emit('jobs', 0)

                                                # Check cache for different job types
                                                should_get_xu = False
                                                if self.__typeJob == 'follow' and int(self.__cache) >= int(self.settings['cacheFollow']):
                                                    should_get_xu = True
                                                elif self.__typeJob == 'love' and int(self.__cache) >= int(self.settings['cacheLove']):
                                                    should_get_xu = True
                                                elif (self.__typeJob == 'cmt' or self.__typeJob == 'comment') and int(self.__cache) >= int(cache_comment_jobs):
                                                    should_get_xu = True
                                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                        f.write(f"[{datetime.now()}] TTC comment cache reached: {self.__cache}/{cache_comment_jobs}\n")

                                                if should_get_xu:
                                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đủ {cache_limit} jobs, đang nhận xu...',self.gui.tableWidget)
                                                    getXuTik()
                                                    # Reset cache after getting xu
                                                    self.__cache = 0
                                                    self.__lstIDTTC = ''
                                                    # Update display after reset
                                                    self.editCellByColumnName.emit(self.index, 'Job/Cache', f'{self.__cache}/{cache_limit} (Total: {self.__total})',self.gui.tableWidget)
                                                    # Add 2 second delay for comment jobs as requested
                                                    if self.__typeJob == 'cmt' or self.__typeJob == 'comment':
                                                        with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                            f.write(f"[{datetime.now()}] TTC comment xu delay: 2 seconds\n")
                                                        time.sleep(2)
                                                else:
                                                    # Show progress towards next xu collection
                                                    remaining = cache_limit - self.__cache
                                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Cần thêm {remaining} jobs để nhận xu ({self.__cache}/{cache_limit})',self.gui.tableWidget)

                                                # Update current xu balance
                                                try:
                                                    xuHT = self.__tuongtaccheo.getXuTTC()
                                                    self.editCellByColumnName.emit(self.index, 'XuHT', str(xuHT['xu']),self.gui.tableWidget)
                                                except Exception as e:
                                                    with open('ttc_debug.log', 'a', encoding='utf-8') as f:
                                                        f.write(f"[{datetime.now()}] TTC failed to get xu balance: {e}\n")
                                                if self.__rateJobs >= self.settings['nextAccFaild']:
                                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
                                                    self.__rateJobs = 0
                                                    if self.settings['reopenChrome'] and self.__reopenChrome < 3: 
                                                        self.__reopenChrome +=1
                                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Khởi động lại Trình Duyệt...',self.gui.tableWidget);time.sleep(3)
                                                        try:
                                                            self.driver.quit();self.__gologin.stop()
                                                        except:pass 
                                                        if self.setupChrome():
                                                            self.__addCookie();time.sleep(3)
                                                            if self.__checkCookie():self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đăng nhập tài khoản TikTok [ {self.userTikTok} ] thành công.',self.gui.tableWidget);break
                                                            self.status = f'Đăng nhập tài khoản TikTok [ {self.userTikTok} ] thất bại.'
                                                            return
                                                        else:return
                                                    else:
                                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
                                                        self.userTikTok,self.passTikTok,self.cookieTik = '', '', '';self.__reopenChrome = 0;self.__deleteCookie();self.__restoreData();
                                                        if runNext() == False:return
                                                
                                                if self.__typeJob == 'follow':
                                                    if self.__dem >= self.settings['maxJobFollow']:
                                                        self.status = f'Đã làm đủ {self.settings["maxJobLove"]} nhiệm vụ {self.__typeJob}.'
                                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
                                                        if runNext() == False:return
                                                if self.__typeJob == 'love':
                                                    if self.__dem >= self.settings['maxJobFollow']:
                                                        self.status = f'Đã làm đủ {self.settings["maxJobLove"]} nhiệm vụ {self.__typeJob}.'
                                                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
                                                        if runNext() == False:return
                                            except:time.sleep(random.randint(3,6))
                                            self.delaySettings('getJob')
                                except Exception as e:
                                    self.__handle_exceptions('Lỗi vòng lặp while tuongtaccheo đầu');
                                    self.delaySettings('error')
                                    self.__checkInternet()
                                    self.apiTikTok.publicLoveAPI()
                    else:
                        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(3);return
                else:self.status = f'Login TTC [ {self.userCoin} ] thất bại!'
            except Exception as e:
                self.__handle_exceptions(e);
                self.delaySettings('error')
                self.__checkInternet()
    
  
    def __activeTokenChrome(self):
        for t in range(10):
            try:
                self.__updateValue()
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Cấu hình account [ {} ] user ( {} ) !'.format(self.userCoin,self.infoTikApi['uniqueId']),self.gui.tableWidget);time.sleep(5)
                ch = self.__traodoisub.activeIdRunToken(self.infoTikApi['uid'])
                self.__print(f'activeIdRunToken {ch["mess"]}')
                if ch['status'] == 'success':
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Cấu hình account [ {} ] user ( {} ) thành công.'.format(self.userCoin,self.infoTikApi['uniqueId']),self.gui.tableWidget)
                    self.driver.get(self.url_tiktok)
                    return True
                self.driver.get('https://traodoisub.com/api/autoclick/abcd/176/?access_token={}'.format(self.tokenTDS));self.driver.set_page_load_timeout(10);time.sleep(1)
                if self.__checkElement(By.XPATH,'//button[text()="Cấu hình"]',5):
                    self.driver.find_element(By.XPATH,'//button[text()="Cấu hình"]').click();time.sleep(3)
                    if self.__checkElement(By.XPATH,'//input[@placeholder="Nhập Username tiktok muốn đặt hoặc thêm cấu hình"]',5):
                        self.driver.find_element(By.XPATH,'//input[@placeholder="Nhập Username tiktok muốn đặt hoặc thêm cấu hình"]').send_keys(self.infoTikApi['uniqueId']);time.sleep(1)
                        if self.__checkElement(By.ID,'chbutton',3):
                            try:
                                self.driver.find_element(By.ID,'chbutton').click()
                            except:self.driver.execute_script("document.getElementById('chbutton').click()")
                            for t in range(10):
                                if 'Vui lòng cập nhật ảnh đại diện không sử đụng ảnh mặc định' in self.driver.page_source:
                                    self.driver.get(self.url_tiktok)
                                    self.uploadAvatar()

                                elif 'Tài khoản tiktok này đang làm nhiệm vụ ở một tài khoản khác, vui lòng dừng mọi thao tác ở tài khoản' in self.driver.page_source:
                                    self.status = 'Tài khoản tiktok này đang làm nhiệm vụ ở một tài khoản khác, vui lòng dừng mọi thao tác ở tài khoản'
                                    self.driver.get(self.url_tiktok)
                                    return False
                                elif 'Cấu hình thành công!' in self.driver.page_source:
                                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Cấu hình account [ {} ] user ( {} ) thành công.'.format(self.userCoin,self.infoTikApi['uniqueId']),self.gui.tableWidget)
                                    return True
                                time.sleep(0.3)
                            self.status = 'Cấu hình account [ {} ] user ( {} ) thất bại.'.format(self.userCoin,self.infoTikApi['uniqueId'])
                            self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget);time.sleep(5)
            except:pass
            time.sleep(random.randint(5,10))
        self.status = f'Cấu hình thất bại vui lòng kiểm tra lại.'
        return False
    
    def __checkCookie(self):
        try:
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra thông tin đăng nhập TikTok !',self.gui.tableWidget )
            self.__getCookieChrome()
            try:
                self.driver.get(self.url_tiktok+'profile');self.driver.set_page_load_timeout(10);time.sleep(3)
                response        = self.driver.page_source
                uid             = response.split('"uid":"')[1].split('","nickName":"')[0]
                nick_name       = response.split('"nickName":"')[1].split('","signature":""')[0]
                uniqueId        = response.split('"uniqueId":"')[1].split('","')[0]
                self.infoTikApi = {'live':True,'uid': uid, 'nickName': nick_name, 'uniqueId': uniqueId} 
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra đăng nhập TikTok thành công. ',self.gui.tableWidget )
                
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra cookie thành công !',self.gui.tableWidget )
                self.editCellByColumnName.emit(self.index, 'UID', str(self.infoTikApi['uniqueId']),self.gui.tableWidget)
                if self.cookieTik == '':self.editCellByColumnName.emit(self.index, 'Cookie', str(self.cookieChrome),self.gui.tableWidget)
                self.editCellByColumnName.emit(self.index, 'Name', str(self.infoTikApi['nickName']),self.gui.tableWidget)
                return True
            except Exception as e: 
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra Cookie TikTok !',self.gui.tableWidget )
                self.apiTikTok  = TikTok_Api(self.cookieChrome)
                self.infoTikApi = self.apiTikTok.infoAccounts()
                if self.infoTikApi['live']:
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Kiểm tra cookie thành công !',self.gui.tableWidget )
                    self.editCellByColumnName.emit(self.index, 'UID', str(self.infoTikApi['uniqueId']),self.gui.tableWidget)
                    if self.cookieTik == '':self.editCellByColumnName.emit(self.index, 'Cookie', str(self.cookieChrome),self.gui.tableWidget)
                    self.editCellByColumnName.emit(self.index, 'Name', str(self.infoTikApi['nickName']),self.gui.tableWidget)
                    return True
        except:pass
        return False
            
    def run(self):
        global used_indexes
        self.editCellByColumnName.emit(self.index, 'Name', '',self.gui.tableWidget)
        self.editCellByColumnName.emit(self.index, 'XuThem', '',self.gui.tableWidget)
        self.editCellByColumnName.emit(self.index, 'Tỷ lệ', '',self.gui.tableWidget)
        self.editCellByColumnName.emit(self.index, 'Job/Cache', '',self.gui.tableWidget)
        try:
            self.status    = 'No such file or directory settings, config, extension'
            self.settings  = json.loads(open(f'{pathConfig}\\settings.json', 'r', encoding="utf-8-sig").read())
            self.config    = json.loads(open(f'{pathConfig}\\config.json', 'r', encoding="utf-8-sig").read())
            self.extension = json.loads(open(f'{pathConfig}\\extension.json', 'r', encoding="utf-8-sig").read())
            self.status    = 'Normal general settings'
        except:time.sleep(2);self.stopMining.emit(self.index);return
        if self.totalThread != 0:
            for giaydoi in range(self.index*self.settings['delayOpenChrome'],0,-1):
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Khởi động luồng sau {} giây !'.format(giaydoi),self.gui.tableWidget)
                time.sleep(1)

        print(self.typeThread)
        if self.setupChrome():
            self.__updateValue()
            if self.typeThread == 'getCookie':
                try:
                    if self.__nextAccount():
                        self.status = f'Đăng nhập tài khoản [ {self.userTikTok} ] thành công.'
                        self.apiTikTok  = TikTok_Api(self.cookieChrome)
                        if self.settings['publicLove']:self.apiTikTok.publicLoveAPI()
                        if self.settings['upAvatar']:self.uploadAvatar()
                        if self.settings['upVideo']:
                            self.uploadVideo()
                            self.apiTikTok  = TikTok_Api(self.cookieChrome)
                            self.infoTikApi = self.apiTikTok.infoAccounts()
                            if self.infoTikApi['live']:
                                print(self.infoTikApi)
                                if int(self.infoTikApi['videoCount']) >= 1:self.status = 'Đăng video thành công!'
                                else:self.status = 'Đăng video thất bại!'
                        self.editBio()
                except Exception as e :self.status = 'Get Cookies Error!!!';self.__handle_exceptions(self.status)
                try:
                    try:self.gui.tableWidgetFuncions.saveDataTable(self.index)
                    except:pass
                    row = next(self.gui.nextThread)
                    self.startMining.emit(int(row), 'getCookie', 0)
                except StopIteration:pass
            elif self.typeThread == 'openChrome':
                self.status = 'Khởi động trình duyệt hoàn tất.';self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget)
                time.sleep(360)
            elif self.settings['miningWeb'] == 'TRAODOISUB'   :self.__earnMoneyTDS()
            elif self.settings['miningWeb'] == 'TUONGTACCHEO' :self.__earnMoneyTTC()
          
        else:self.status = 'Mở trình duyệt thất bại vui lòng kiểm tra lại phiên bản chromedriver.exe'
        self.editCellByColumnName.emit(self.index, 'Trạng Thái', str(self.status),self.gui.tableWidget)
        self.stopMining.emit(self.index)
    
    def LoginTDS(self):
        info = {'Trạng Thái': "error", 'data': {'user': self.userCoin, 'xu': 0} ,'mess': "Sai tài khoản hoặc mật khẩu hoặc bị chặn IP!"}
        try:
            print(self.proxyRequests)
            self.__traodoisub = TDS(self.userCoin, self.passCoin)
            self.__traodoisub.addProxy(self.proxyRequests)
            info = self.__traodoisub.loginTDS()
            if info['status'] == 'success':
                self.xuHT, self.tokenTDS = self.__traodoisub.getUser()
                self.editCellByColumnName.emit(self.index, 'XuHT', str(info['data']['xu']),self.gui.tableWidget)
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Đăng nhập thành công. Xu hiện tại {}'.format(info['data']['xu']),self.gui.tableWidget)
                return info
        except Exception as e:
            self.__handle_exceptions('Lỗi đăng nhập TraoDoiSub')
        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đăng nhập [ {self.userCoin} ] TDS thất bại vui lòng kiểm tra lại!', self.gui.tableWidget)
        return info

    def LoginTTC(self):
        self.info = {'Trạng Thái': "error", 'data': {'user': self.userCoin, 'xu': 0} ,'mess': "Sai tài khoản hoặc mật khẩu"}
        try:
            self.__tuongtaccheo = TTC(self.userCoin, self.passCoin)
            self.__tuongtaccheo.addProxy(self.proxyRequests)
            self.info = self.__tuongtaccheo.loginTTC()
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Đăng nhập thành công. Xu hiện tại {}'.format(self.info['data']['xu']),self.gui.tableWidget);time.sleep(5)
            if self.info['status'] == 'success':
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Đăng nhập thành công. Xu hiện tại {}'.format(self.info['data']['xu']),self.gui.tableWidget)
                self.editCellByColumnName.emit(self.index, 'XuHT', str(self.info['data']['xu']),self.gui.tableWidget)
                return self.info
            else:
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Login TTC [ {} ] thất bại vui lòng kiểm tra lại!'.format(self.userCoin),self.gui.tableWidget );time.sleep(5)
        except Exception as e:
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Login TTC thất bại vui lòng kiểm tra lại!',self.gui.tableWidget);time.sleep(5)
            self.__handle_exceptions('Lỗi đăng nhập TuongTacCheo')
        return self.info
    
    def delaySettings(self, type):
        if type == 'follow':
            clickRD = random.randint(1,3)
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Nhấn Theo dõi ngẫu nhiên sau {} giây'.format(clickRD),self.gui.tableWidget)
            time.sleep(clickRD)
        if type == 'getXu':
            for i in range(self.settings['delayGetXuValue'],0,-1):
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Nhận xu sau sau {i} giây',self.gui.tableWidget)
                time.sleep(1)
        if type == 'getJob':
            for i in range(self.settings['delayGetJobValue'],0,-1):
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Làm nhiệm vụ tiếp sau {i} giây',self.gui.tableWidget)
                time.sleep(1)
        if type == 'activated':
            for i in range(self.settings['delayCH'],0,-1):
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Lấy nhiệm vụ sau {i} giây',self.gui.tableWidget)
                time.sleep(1)
        if type == 'click':
            for t in range(random.randint(2,3),0,-1):
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Nhấn {self.__typeJob.upper()} sau {t} giây',self.gui.tableWidget)
                time.sleep(1)
        if type == 'error':
            for t in range(15,0,-1):
                # Ghi log chi tiết lỗi vào file log
                try:
                    import traceback
                    with open('error_detail.log', 'a', encoding='utf-8') as f:
                        f.write(f"[ERROR] {datetime.now()} | Index: {self.index} | Status: {self.status}\n")
                        f.write(traceback.format_exc() + '\n')
                except Exception as log_err:
                    pass
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'[ {self.__error} ] Có lỗi xảy ra [ Coppy ERROR Send Admin ] chạy lại sau {t} giây',self.gui.tableWidget)
                time.sleep(1)
        if type == 'proxy':
            for t in range(120,0,-1):
                self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Nhận thành công 0xu chạy tiếp sau {t} giây',self.gui.tableWidget)
                time.sleep(1)
    
    def __print(self, text):
        current_time = datetime.now()
        print(f'{Fore.LIGHTYELLOW_EX}[ {self.index+1} ] [ {current_time} ] : {text}')
        
    def __restoreData(self):
        if self.dataAccTikTok != '':
            for index, columnName in zip(range(3),['UID','Password','Cookie']):
                self.editCellByColumnName.emit(self.index,columnName , '',self.gui.tableWidget)         
    
    def __deleteAccounts(self):
        if self.dataAccTikTok != '':
            file_path = os.path.join(pathDataTikTok, 'tiktok.txt')
            # Đọc nội dung từ tệp tin
            with open(file_path, 'r') as file:
                lines = file.readlines()

            lines = [line.strip() for line in lines if line.strip() != self.dataAccTikTok]
            with open(file_path, 'w') as file:
                file.write('\n'.join(lines))
            outfile = os.path.join(pathDataTikTok, 'used.txt')
            writeFile(outfile,f'{self.dataAccTikTok}|{self.status}\n')
   
    def __deleteCookie(self):
        try:
            self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Xóa dữ liệu Chrome.',self.gui.tableWidget)
            self.driver.delete_all_cookies()  # Xóa cookie

            # Làm sạch lịch sử duyệt
            self.driver.execute_script("window.localStorage.clear();")  # Xóa localStorage
            self.driver.execute_script("window.sessionStorage.clear();")  # Xóa sessionStorage

            # Làm sạch cache
            self.driver.execute_script("window.open('chrome://settings/clearBrowserData', '_blank');")  # Mở trang clearBrowserData trong tab mới
            self.driver.switch_to.window(self.driver.window_handles[-1])  # Chuyển tới tab mới
            self.driver.implicitly_wait(5)  # Đợi 5 giây để trang load
            self.driver.refresh();self.driver.get(self.url_tiktok)
        except:pass
   
    def __addCookie(self):
        self.editCellByColumnName.emit(self.index, 'Trạng Thái', '[ Add Cookie ] Add Cookie TikTok',self.gui.tableWidget)
        apiTik = TikTok_Api(self.cookieTik)
        if apiTik.infoAccounts() != None:
            cookieDict = str(self.cookieTik).split(";")
            try:cookieDict.remove('')
            except:pass
            for x in cookieDict:
                try:
                    name = x.split("=")[0]
                    value = x.split(name+"=")[1]
                    if  " " in name:
                        name = name.replace(" ", '')
                    self.driver.add_cookie({'name': name, 'value': value})
                    self.editCellByColumnName.emit(self.index, 'Trạng Thái', '[ Add Cookie ] Add Cookie TikTok hoàn tất.',self.gui.tableWidget)
                except:
                    pass
            self.driver.get("https://tiktok.com/");self.driver.set_page_load_timeout(10)
            if 'Block-Media' in self.extension['ExtensionChecked']:self.__blockImage('BLOCK_ON')
            return True
        else: self.editCellByColumnName.emit(self.index, 'Trạng Thái', 'Cookie die vui lòng kiểm tra lại !',self.gui.tableWidget);return False
        
    def __getCookieChrome(self):
        try:
            self.cookieChrome = ""
            ck_get = self.driver.get_cookies()
            for value in ck_get:
                self.cookieChrome+=value['name']+'='+value['value']+";"
        except:self.cookieChrome = ''

    def stop(self):
        self.__updateValue()
        global used_indexes
        try:used_indexes.remove(self.window_params)
        except:pass
        try:
            threading.Thread(target=self.driver.quit, args=()).start()
            if self.settings['Browser'] == 'GoLogin':threading.Thread(target=self.__gologin.stop, args=()).start()
            if self.settings['Browser'] == 'HideMyAcc':threading.Thread(target=self.__hidemyacc.stop, args=(self.idProfile)).start()
        except Exception as e:pass
        # try:self.gui.tableWidgetFuncions.saveDataTable(self.index)
        # except:pass
        self.editCellByColumnName.emit(self.index, 'Trạng Thái', f'Đã kết thúc, trước đó | {self.status}',self.gui.tableWidget)
        self.terminate()

    def loginTikTok(self):
        """
        Hàm loginTikTok được thêm để sửa lỗi AttributeError.
        Gọi hàm __switchWindow để thực hiện đăng nhập TikTok bằng user/pass.
        """
        return self.__switchWindow()


