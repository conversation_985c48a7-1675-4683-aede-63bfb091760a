[2025-07-14 10:27:18.706796] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:27:19.001733] Request successful, status: 200
[2025-07-14 10:27:19.003712] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:27:19.146430] Request successful, status: 200
[2025-07-14 10:27:34.512664] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 10:27:35.011156] Request successful, status: 200
[2025-07-14 10:27:40.014569] TTC getJob called for type: comment
[2025-07-14 10:27:40.015067] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=soosscb6bio4ahbnal3ch1d093; Path=/'}
[2025-07-14 10:27:40.015565] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:27:40.148304] Request successful, status: 200
[2025-07-14 10:27:40.149303] TTC comment response status: 200
[2025-07-14 10:27:40.149303] TTC comment response text: {"error":"Lấy nhiệm vụ sau 21 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":21}...
[2025-07-14 10:27:40.149801] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 10:27:40.150301] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 21 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 21}
[2025-07-14 10:28:01.159619] TTC getJob called for type: comment
[2025-07-14 10:28:01.159619] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=soosscb6bio4ahbnal3ch1d093; Path=/'}
[2025-07-14 10:28:01.160634] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:28:01.320819] Request successful, status: 200
[2025-07-14 10:28:01.321325] TTC comment response status: 200
[2025-07-14 10:28:01.321325] TTC comment response text: [{"idpost":"7517184022275951892","link":"https:\/\/www.tiktok.com\/@thulaminhthu\/video\/7517184022275951892","nd":"[\"trai \\u0111\\u1eb9p th\\u00edch trai \\u0111\\u1eb9p \\ud83e\\udd2d\",\"oppaaaaaa\",\"idol \\u0111i \\u0111u idol g\\u1eb7p 1 idol \\u0111i \\u0111u idol \\ud83e\\udd21\",\"To\\u00e0n c\\u00e1c a \\u0111zai\",\"cute qu\\u00e1 troiiii oiiii\"]"},{"idpost":"7526504322369064199","link":"https:\/\/www.tiktok.com\/@nhisophia999\/photo\/7526504322369064199","nd":"[\"N\\u00e0y l\\u00e...
[2025-07-14 10:28:01.322319] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 10:28:01.322815] TTC comment - Jobs list detected, length: 36
[2025-07-14 10:28:01.370223] TTC starting jobs iteration: 36 jobs
[2025-07-14 10:28:01.370707] TTC comment settings: max=12345, cache=2
[2025-07-14 10:28:01.371720] TTC processing job 1: 7517184022275951892, https://www.tiktok.com/@thulaminhthu/video/7517184022275951892
[2025-07-14 10:28:01.372355] TTC comment content found: ["trai \u0111\u1eb9p th\u00edch trai \u0111\u1eb9p \ud83e\udd2d","oppaaaaaa","idol \u0111i \u0111u i...
[2025-07-14 10:28:01.373218] TTC navigating to: https://www.tiktok.com/@thulaminhthu/video/7517184022275951892
[2025-07-14 10:28:05.872403] TTC navigation successful
[2025-07-14 10:28:05.872917] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:28:21.661269] TTC tried Escape key 3 times to close popups
[2025-07-14 10:28:23.082477] TTC forced close keyboard shortcuts popup with multiple ESC
[2025-07-14 10:28:23.082994] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:28:23.103456] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:28:35.384890] TTC tried Escape key 3 times to close popups
[2025-07-14 10:28:36.532869] TTC comment input clicked with ActionChains
[2025-07-14 10:28:38.276472] TTC cleaned comment text: 'oppaaaaaa' (length: 9)
[2025-07-14 10:28:38.276972] TTC typing comment: oppaaaaaa...
[2025-07-14 10:28:39.882346] TTC typing method 1 result: expected='oppaaaaaa', actual='ppaaaaaa'
[2025-07-14 10:28:39.882346] TTC typing method 1 success: 8/9 chars
[2025-07-14 10:28:40.889385] TTC verify attempt 1: expected='oppaaaaaa...', actual='ppaaaaaa...'
[2025-07-14 10:28:41.421871] TTC comment input current value: 'ppaaaaaa...'
[2025-07-14 10:28:41.422370] TTC comment text verified in input
[2025-07-14 10:28:41.429357] TTC detected missing first character, fixing...
[2025-07-14 10:28:42.257743] TTC final verification passed: 'oppaaaaaa...'
[2025-07-14 10:28:43.282901] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:28:43.589594] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:28:45.646669] TTC comment not found in list (may still be processing)
[2025-07-14 10:28:45.647169] TTC comment posted successfully
[2025-07-14 10:28:45.648168] TTC __clickXmlJobs result: True
[2025-07-14 10:29:05.653208] TTC processing job 2: 7526504322369064199, https://www.tiktok.com/@nhisophia999/photo/7526504322369064199
[2025-07-14 10:29:05.654206] TTC comment content found: ["N\u00e0y l\u00e0 l\u1ec5 g\u00ec \u00e1 b\u1ea1n","V\u1ebd T\u1ebft \u0111oan ng\u1ecd y ch\u1ebf"...
[2025-07-14 10:29:05.655203] TTC navigating to: https://www.tiktok.com/@nhisophia999/photo/7526504322369064199
[2025-07-14 10:29:07.383979] TTC navigation successful
[2025-07-14 10:29:07.384477] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:29:23.278363] TTC tried Escape key 3 times to close popups
[2025-07-14 10:29:23.821248] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:29:23.842712] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:29:36.126460] TTC tried Escape key 3 times to close popups
[2025-07-14 10:29:37.323626] TTC comment input clicked with ActionChains
[2025-07-14 10:29:39.125448] TTC cleaned comment text: 'Check ib m bn ơi' (length: 16)
[2025-07-14 10:29:39.125947] TTC typing comment: Check ib m bn ơi...
[2025-07-14 10:29:40.770405] TTC typing method 1 result: expected='Check ib m bn ơi', actual='heck ib m bn ơi'
[2025-07-14 10:29:40.771408] TTC typing method 1 success: 15/16 chars
[2025-07-14 10:29:41.795421] TTC verify attempt 1: expected='Check ib m bn ơi...', actual='heck ib m bn ơi...'
[2025-07-14 10:29:42.326878] TTC comment input current value: 'heck ib m bn ơi...'
[2025-07-14 10:29:42.327377] TTC comment text verified in input
[2025-07-14 10:29:42.333884] TTC detected missing first character, fixing...
[2025-07-14 10:29:43.160285] TTC final verification passed: 'Check ib m bn ơi...'
[2025-07-14 10:29:44.187295] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:29:44.510698] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:29:46.551184] TTC comment verified in comment list
[2025-07-14 10:29:46.551677] TTC comment posted successfully
[2025-07-14 10:29:46.552181] TTC __clickXmlJobs result: True
[2025-07-14 10:29:46.552675] TTC comment cache reached: 2/2
[2025-07-14 10:30:09.558508] TTC processing job 3: 7526509100285414664, https://www.tiktok.com/@hongvydaily_90/video/7526509100285414664
[2025-07-14 10:30:09.558989] TTC comment content found: ["Nh\u00ecn x\u1ecbn qu\u00e1, ch\u1ecb share b\u00ed k\u00edp \u0111i","Nh\u1eafn tin ch\u1ecb r\u1...
[2025-07-14 10:30:09.559987] TTC navigating to: https://www.tiktok.com/@hongvydaily_90/video/7526509100285414664
[2025-07-14 10:30:14.203341] TTC navigation successful
[2025-07-14 10:30:14.203987] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:30:30.000797] TTC tried Escape key 3 times to close popups
[2025-07-14 10:30:31.444623] TTC forced close keyboard shortcuts popup with multiple ESC
[2025-07-14 10:30:31.445102] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:30:31.464586] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:30:43.687074] TTC tried Escape key 3 times to close popups
[2025-07-14 10:30:44.886910] TTC comment input clicked with ActionChains
[2025-07-14 10:30:46.637480] TTC cleaned comment text: 'Xin vía nha chị' (length: 15)
[2025-07-14 10:30:46.637979] TTC typing comment: Xin vía nha chị...
[2025-07-14 10:30:48.399588] TTC typing method 1 result: expected='Xin vía nha chị', actual='in vía nha chị'
[2025-07-14 10:30:48.400087] TTC typing method 1 success: 14/15 chars
[2025-07-14 10:30:49.408148] TTC verify attempt 1: expected='Xin vía nha chị...', actual='in vía nha chị...'
[2025-07-14 10:30:49.937628] TTC comment input current value: 'in vía nha chị...'
[2025-07-14 10:30:49.938626] TTC comment text verified in input
[2025-07-14 10:30:49.944615] TTC detected missing first character, fixing...
[2025-07-14 10:30:50.770526] TTC final verification passed: 'Xin vía nha chị...'
[2025-07-14 10:30:51.792089] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:30:52.118462] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:30:54.167942] TTC comment not found in list (may still be processing)
[2025-07-14 10:30:54.168459] TTC comment posted successfully
[2025-07-14 10:30:54.168940] TTC __clickXmlJobs result: True
[2025-07-14 10:30:54.168940] TTC comment cache reached: 3/2
[2025-07-14 10:31:17.176126] TTC processing job 4: 7525926507919854855, https://www.tiktok.com/@tmc1110_08/photo/7525926507919854855
[2025-07-14 10:31:17.176642] TTC comment content found: ["Ah c\u00f3 b\u1ed3 ch\u01b0a \ud83c\udf39","Xin fb \u0111i a"]...
[2025-07-14 10:31:17.177651] TTC navigating to: https://www.tiktok.com/@tmc1110_08/photo/7525926507919854855
[2025-07-14 10:31:18.702363] TTC navigation successful
[2025-07-14 10:31:18.702879] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:31:34.269449] TTC tried Escape key 3 times to close popups
[2025-07-14 10:31:34.803906] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:31:34.815386] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:31:46.987101] TTC tried Escape key 3 times to close popups
[2025-07-14 10:31:48.169837] TTC comment input clicked with ActionChains
[2025-07-14 10:31:49.918482] TTC cleaned comment text: 'Ah có bồ chưa' (length: 13)
[2025-07-14 10:31:49.918980] TTC typing comment: Ah có bồ chưa...
[2025-07-14 10:31:51.534929] TTC typing method 1 result: expected='Ah có bồ chưa', actual='h có bồ chưa'
[2025-07-14 10:31:51.535445] TTC typing method 1 success: 12/13 chars
[2025-07-14 10:31:52.542207] TTC verify attempt 1: expected='Ah có bồ chưa...', actual='h có bồ chưa...'
[2025-07-14 10:31:53.066510] TTC comment input current value: 'h có bồ chưa...'
[2025-07-14 10:31:53.066510] TTC comment text verified in input
[2025-07-14 10:31:53.073514] TTC detected missing first character, fixing...
[2025-07-14 10:31:53.896973] TTC final verification passed: 'Ah có bồ chưa...'
[2025-07-14 10:31:54.922425] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:31:55.242310] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:31:57.306372] TTC comment not found in list (may still be processing)
[2025-07-14 10:31:57.306871] TTC comment posted successfully
[2025-07-14 10:31:57.307867] TTC __clickXmlJobs result: True
[2025-07-14 10:31:57.308351] TTC comment cache reached: 4/2
[2025-07-14 10:32:20.315047] TTC processing job 5: 7526423513822022930, https://www.tiktok.com/@anquoc2211/video/7526423513822022930
[2025-07-14 10:32:20.315552] TTC comment content found: ["Cho e xin \u1ea3nh \u0111\u1ea7u v\u1edbi \u1ea1\ud83d\ude3b","T\u00ean nh\u00ecn quen qu\u00e1\ud...
[2025-07-14 10:32:20.316046] TTC navigating to: https://www.tiktok.com/@anquoc2211/video/7526423513822022930
[2025-07-14 10:32:24.926209] TTC navigation successful
[2025-07-14 10:32:24.926708] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:32:39.692332] TTC tried Escape key 3 times to close popups
[2025-07-14 10:32:41.004805] TTC forced close keyboard shortcuts popup with multiple ESC
[2025-07-14 10:32:41.005304] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:32:41.017891] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:32:53.425665] TTC tried Escape key 3 times to close popups
[2025-07-14 10:32:54.573632] TTC comment input clicked with ActionChains
[2025-07-14 10:32:56.345693] TTC cleaned comment text: 'Đỉnh quá shop' (length: 13)
[2025-07-14 10:32:56.346192] TTC typing comment: Đỉnh quá shop...
[2025-07-14 10:32:58.063773] TTC typing method 1 result: expected='Đỉnh quá shop', actual='ỉnh quá shop'
[2025-07-14 10:32:58.064272] TTC typing method 1 success: 12/13 chars
[2025-07-14 10:32:59.070380] TTC verify attempt 1: expected='Đỉnh quá shop...', actual='ỉnh quá shop...'
[2025-07-14 10:32:59.599883] TTC comment input current value: 'ỉnh quá shop...'
[2025-07-14 10:32:59.600385] TTC comment text verified in input
[2025-07-14 10:32:59.607369] TTC detected missing first character, fixing...
[2025-07-14 10:33:00.445729] TTC final verification passed: 'Đỉnh quá shop...'
[2025-07-14 10:33:01.472130] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:33:01.785029] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:33:03.816858] TTC comment verified in comment list
[2025-07-14 10:33:03.817372] TTC comment posted successfully
[2025-07-14 10:33:03.817872] TTC __clickXmlJobs result: True
[2025-07-14 10:33:03.817872] TTC comment cache reached: 5/2
[2025-07-14 10:33:25.825466] TTC processing job 6: 7526552762474171664, https://www.tiktok.com/@em.trang.bs/video/7526552762474171664
[2025-07-14 10:33:25.826468] TTC comment content found: ["Cho m\u00ecnh xem chi ti\u1ebft","Gi\u00e1 sao b\u1ea1n \u01a1i","Check ib m\u00ecnh v\u1edbi","Ib...
[2025-07-14 10:33:25.826962] TTC navigating to: https://www.tiktok.com/@em.trang.bs/video/7526552762474171664
[2025-07-14 10:33:30.558933] TTC navigation successful
[2025-07-14 10:33:30.559915] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:33:46.395895] TTC tried Escape key 3 times to close popups
[2025-07-14 10:33:47.776100] TTC forced close keyboard shortcuts popup with multiple ESC
[2025-07-14 10:33:47.776582] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:33:47.799055] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:33:59.967041] TTC tried Escape key 3 times to close popups
[2025-07-14 10:34:01.138290] TTC comment input clicked with ActionChains
[2025-07-14 10:34:02.863004] TTC cleaned comment text: 'Cần thông tin chi tiết' (length: 22)
[2025-07-14 10:34:02.863503] TTC typing comment: Cần thông tin chi tiết...
[2025-07-14 10:34:05.023410] TTC typing method 1 result: expected='Cần thông tin chi tiết', actual='ần thông tin chi tiết'
[2025-07-14 10:34:05.023886] TTC typing method 1 success: 21/22 chars
[2025-07-14 10:34:06.030466] TTC verify attempt 1: expected='Cần thông tin chi tiết...', actual='ần thông tin chi tiết...'
[2025-07-14 10:34:06.561455] TTC comment input current value: 'ần thông tin chi tiết...'
[2025-07-14 10:34:06.561954] TTC comment text verified in input
[2025-07-14 10:34:06.567942] TTC detected missing first character, fixing...
[2025-07-14 10:34:07.394393] TTC final verification passed: 'Cần thông tin chi tiết...'
[2025-07-14 10:34:08.419993] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:34:08.734376] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:34:10.781500] TTC comment not found in list (may still be processing)
[2025-07-14 10:34:10.781500] TTC comment posted successfully
[2025-07-14 10:34:10.782001] TTC __clickXmlJobs result: True
[2025-07-14 10:34:10.782497] TTC comment cache reached: 6/2
[2025-07-14 10:34:31.786700] TTC processing job 7: 7526507696724462855, https://www.tiktok.com/@dailyngocngoc/video/7526507696724462855
[2025-07-14 10:34:31.787178] TTC comment content found: ["th\u00e0nh t\u00e2m xin v\u00eda ra \u0111\u01a1n nhi\u1ec1u gi\u1ed1ng ch\u1ecb \u1ea1","ch\u1ecb...
[2025-07-14 10:34:31.787678] TTC navigating to: https://www.tiktok.com/@dailyngocngoc/video/7526507696724462855
[2025-07-14 10:34:36.464775] TTC navigation successful
[2025-07-14 10:34:36.465771] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:34:51.283836] TTC tried Escape key 3 times to close popups
[2025-07-14 10:34:52.753934] TTC forced close keyboard shortcuts popup with multiple ESC
[2025-07-14 10:34:52.754692] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:34:52.777389] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:35:05.032388] TTC tried Escape key 3 times to close popups
[2025-07-14 10:35:06.200962] TTC comment input clicked with ActionChains
[2025-07-14 10:35:07.961351] TTC cleaned comment text: 'em nhắn tin chị rồi nhé, chị check rồi hướng dẫn em làm với ạ' (length: 61)
[2025-07-14 10:35:07.961872] TTC typing comment: em nhắn tin chị rồi nhé, chị check rồi hướng dẫn e...
[2025-07-14 10:35:11.285103] TTC typing method 1 result: expected='em nhắn tin chị rồi nhé, chị check rồi hướng dẫn em làm với ạ', actual='m nhắn tin chị rồi nhé, chị check rồi hướng dẫn em làm với ạ'
[2025-07-14 10:35:11.285602] TTC typing method 1 success: 60/61 chars
[2025-07-14 10:35:12.294671] TTC verify attempt 1: expected='em nhắn tin chị rồi nhé, chị c...', actual='m nhắn tin chị rồi nhé, chị ch...'
[2025-07-14 10:35:12.826980] TTC comment input current value: 'm nhắn tin chị rồi nhé, chị check rồi hướng dẫn em...'
[2025-07-14 10:35:12.827479] TTC comment text verified in input
[2025-07-14 10:35:12.835977] TTC detected missing first character, fixing...
[2025-07-14 10:35:13.674024] TTC final verification passed: 'em nhắn tin chị rồi nhé, chị check rồi hướng dẫn e...'
[2025-07-14 10:35:14.704581] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:35:14.999503] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:35:17.027412] TTC comment verified in comment list
[2025-07-14 10:35:17.027931] TTC comment posted successfully
[2025-07-14 10:35:17.028428] TTC __clickXmlJobs result: True
[2025-07-14 10:35:17.028428] TTC comment cache reached: 7/2
[2025-07-14 10:35:38.036378] TTC processing job 8: 7526567702152875282, https://www.tiktok.com/@quynhquoc53/video/7526567702152875282
[2025-07-14 10:35:38.036878] TTC comment content found: ["\u0110en v\u00ec n\u1eafng gi\u00f3, v\u00ec m\u01b0u sinh ch\u1ee9 kh\u00f4ng h\u1ec1 bu\u1ed3n",...
[2025-07-14 10:35:38.037858] TTC navigating to: https://www.tiktok.com/@quynhquoc53/video/7526567702152875282
[2025-07-14 10:35:42.508878] TTC navigation successful
[2025-07-14 10:35:42.509786] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:35:58.274500] TTC tried Escape key 3 times to close popups
[2025-07-14 10:35:59.695732] TTC forced close keyboard shortcuts popup with multiple ESC
[2025-07-14 10:35:59.696572] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:35:59.719045] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:36:11.967129] TTC tried Escape key 3 times to close popups
[2025-07-14 10:36:13.136228] TTC comment input clicked with ActionChains
[2025-07-14 10:36:14.878252] TTC cleaned comment text: 'Đen vì nắng gió, vì mưu sinh chứ không hề buồn' (length: 46)
[2025-07-14 10:36:14.878754] TTC typing comment: Đen vì nắng gió, vì mưu sinh chứ không hề buồn...
[2025-07-14 10:36:17.449287] TTC typing method 1 result: expected='Đen vì nắng gió, vì mưu sinh chứ không hề buồn', actual='en vì nắng gió, vì mưu sinh chứ không hề buồn'
[2025-07-14 10:36:17.450285] TTC typing method 1 success: 45/46 chars
[2025-07-14 10:36:18.457773] TTC verify attempt 1: expected='Đen vì nắng gió, vì mưu sinh c...', actual='en vì nắng gió, vì mưu sinh ch...'
[2025-07-14 10:36:18.987260] TTC comment input current value: 'en vì nắng gió, vì mưu sinh chứ không hề buồn...'
[2025-07-14 10:36:18.987767] TTC comment text verified in input
[2025-07-14 10:36:18.993238] TTC detected missing first character, fixing...
[2025-07-14 10:36:19.817175] TTC final verification passed: 'Đen vì nắng gió, vì mưu sinh chứ không hề buồn...'
[2025-07-14 10:36:20.844600] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:36:21.158214] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:36:23.206309] TTC comment not found in list (may still be processing)
[2025-07-14 10:36:23.206824] TTC comment posted successfully
[2025-07-14 10:36:23.207307] TTC __clickXmlJobs result: True
[2025-07-14 10:36:23.207307] TTC comment cache reached: 8/2
[2025-07-14 10:36:43.213446] TTC processing job 9: 7525840310111423758, https://www.tiktok.com/@huhumayman1/video/7525840310111423758
[2025-07-14 10:36:43.214339] TTC comment content found: ["\u1ed4n \u00e1p","Xin khung gi\u1edd h\u00f4m nay","Cho xin link v\u1edbi b\u00e9 \u01a1i","C\u1ed...
[2025-07-14 10:36:43.214831] TTC navigating to: https://www.tiktok.com/@huhumayman1/video/7525840310111423758
[2025-07-14 10:36:47.923031] TTC navigation successful
[2025-07-14 10:36:47.923533] TTC calling __clickXmlJobs with comment: True
