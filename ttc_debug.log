﻿[2025-07-14 10:38:15.525738] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:38:15.899019] Request successful, status: 200
[2025-07-14 10:38:15.899531] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:38:16.043757] Request successful, status: 200
[2025-07-14 10:38:30.482533] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 10:38:31.450387] Request successful, status: 200
[2025-07-14 10:38:36.453698] TTC getJob called for type: comment
[2025-07-14 10:38:36.453698] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=eoa7n8nom0v5jmdc21ra1rvi07; Path=/'}
[2025-07-14 10:38:36.454698] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:38:36.663934] Request successful, status: 200
[2025-07-14 10:38:36.664433] TTC comment response status: 200
[2025-07-14 10:38:36.664433] TTC comment response text: {"error":"Lấy nhiệm vụ sau 9 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":9}...
[2025-07-14 10:38:36.664932] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 10:38:36.665429] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 9 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 9}
[2025-07-14 10:38:45.671992] TTC getJob called for type: comment
[2025-07-14 10:38:45.671992] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=eoa7n8nom0v5jmdc21ra1rvi07; Path=/'}
[2025-07-14 10:38:45.672491] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:38:45.836314] Request successful, status: 200
[2025-07-14 10:38:45.836796] TTC comment response status: 200
[2025-07-14 10:38:45.836796] TTC comment response text: [{"idpost":"7526440937808006418","link":"https:\/\/www.tiktok.com\/@quang.huy.top\/photo\/7526440937808006418","nd":"[\"Kh\\u00f4ng c\\u1ea7n trend m\\u1ea1nh ch\\u1ec9 c\\u1ea7n \\u0111\\u00fang c\\u00e1ch nh\\u01b0 anh ch\\u1ec9 l\\u00e0 \\u0111\\u01b0\\u1ee3c\",\"L\\u00e0m v\\u00e0i c\\u00e1i l\\u00e0 l\\u00ean trend c\\u1ea3m \\u01a1n anh nha\",\"T\\u1eadn t\\u00e2m v\\u00e0 kh\\u00f4ng h\\u1ec1 h\\u1eddi h\\u1ee3t c\\u1ea3m \\u01a1n anh nhi\\u1ec1u\",\"Ai \\u0111ang ch\\u1eadt v\\u1eadt l\\...
[2025-07-14 10:38:45.838310] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 10:38:45.838809] TTC comment - Jobs list detected, length: 36
[2025-07-14 10:38:45.885704] TTC starting jobs iteration: 36 jobs
[2025-07-14 10:38:45.886219] TTC comment settings: max=12345, cache=2
[2025-07-14 10:38:45.886717] TTC processing job 1: 7526440937808006418, https://www.tiktok.com/@quang.huy.top/photo/7526440937808006418
[2025-07-14 10:38:45.887201] TTC comment content found: ["Kh\u00f4ng c\u1ea7n trend m\u1ea1nh ch\u1ec9 c\u1ea7n \u0111\u00fang c\u00e1ch nh\u01b0 anh ch\u1e...
[2025-07-14 10:38:45.887716] TTC navigating to: https://www.tiktok.com/@quang.huy.top/photo/7526440937808006418
[2025-07-14 10:38:47.804132] TTC navigation successful
[2025-07-14 10:38:47.805133] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:39:03.091764] TTC tried Escape key 3 times to close popups
[2025-07-14 10:39:06.346978] TTC detected popup by selector: div[class*="Modal"]
[2025-07-14 10:39:06.347958] TTC starting aggressive popup closing
[2025-07-14 10:39:10.774499] TTC force removed modals with JavaScript
[2025-07-14 10:39:13.431000] TTC successfully closed keyboard shortcuts popup
[2025-07-14 10:39:13.431481] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:39:13.447469] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:39:25.702667] TTC tried Escape key 3 times to close popups
[2025-07-14 10:39:26.388179] TTC comment input clicked with ActionChains (fast)
[2025-07-14 10:39:28.004431] TTC cleaned comment text: 'Làm vài cái là lên trend cảm ơn anh nha' (length: 39)
[2025-07-14 10:39:28.005430] TTC typing comment: Làm vài cái là lên trend cảm ơn anh nha...
[2025-07-14 10:39:30.009097] TTC typing method 1 result: expected='Làm vài cái là lên trend cảm ơn anh nha', actual='àm vài cái là lên trend cảm ơn anh nha'
[2025-07-14 10:39:30.009599] TTC typing method 1 success: 38/39 chars
[2025-07-14 10:39:31.017745] TTC verify attempt 1: expected='Làm vài cái là lên trend cảm ơ...', actual='àm vài cái là lên trend cảm ơn...'
[2025-07-14 10:39:31.558204] TTC comment input current value: 'àm vài cái là lên trend cảm ơn anh nha...'
[2025-07-14 10:39:31.558704] TTC comment text verified in input
[2025-07-14 10:39:31.565191] TTC detected missing first character, fixing...
[2025-07-14 10:39:32.396678] TTC final verification passed: 'Làm vài cái là lên trend cảm ơn anh nha...'
[2025-07-14 10:39:32.930165] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:39:33.260021] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:39:34.839495] TTC comment not found in list (may still be processing)
[2025-07-14 10:39:34.839994] TTC comment posted successfully
[2025-07-14 10:39:34.840494] TTC __clickXmlJobs result: True
[2025-07-14 10:39:34.840975] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:39:34.996682] Request successful, status: 200
[2025-07-14 10:39:52.003600] TTC processing job 2: 7526606098409819410, https://www.tiktok.com/@kplytran/video/7526606098409819410
[2025-07-14 10:39:52.004597] TTC comment content found: ["S\u1edbm","Cho tui xin 1 fl nha","\u2764\ufe0f\u2764\ufe0f\u2764\ufe0f","Cho e xin 1 fl , e c\u1ee...
[2025-07-14 10:39:52.005099] TTC navigating to: https://www.tiktok.com/@kplytran/video/7526606098409819410
[2025-07-14 10:39:56.631551] TTC navigation successful
[2025-07-14 10:39:56.632549] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:40:11.732846] TTC tried Escape key 3 times to close popups
[2025-07-14 10:40:11.759280] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:40:11.759799] TTC starting aggressive popup closing
[2025-07-14 10:40:16.111764] TTC force removed modals with JavaScript
[2025-07-14 10:40:16.628555] TTC popup still visible after all attempts
[2025-07-14 10:40:16.629054] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:40:16.651010] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:40:28.977331] TTC tried Escape key 3 times to close popups
[2025-07-14 10:40:29.697973] TTC comment input clicked with ActionChains (fast)
[2025-07-14 10:40:31.271789] TTC cleaned comment text: 'Lens 14.5 đúng không á' (length: 22)
[2025-07-14 10:40:31.272288] TTC typing comment: Lens 14.5 đúng không á...
[2025-07-14 10:40:32.975055] TTC typing method 1 result: expected='Lens 14.5 đúng không á', actual='ens 14.5 đúng không á'
[2025-07-14 10:40:32.975558] TTC typing method 1 success: 21/22 chars
[2025-07-14 10:40:33.983145] TTC verify attempt 1: expected='Lens 14.5 đúng không á...', actual='ens 14.5 đúng không á...'
[2025-07-14 10:40:34.523837] TTC comment input current value: 'ens 14.5 đúng không á...'
[2025-07-14 10:40:34.524335] TTC comment text verified in input
[2025-07-14 10:40:34.531823] TTC detected missing first character, fixing...
[2025-07-14 10:40:35.369096] TTC final verification passed: 'Lens 14.5 đúng không á...'
[2025-07-14 10:40:35.910846] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:40:36.243446] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:40:37.810994] TTC comment not found in list (may still be processing)
[2025-07-14 10:40:37.811498] TTC comment posted successfully
[2025-07-14 10:40:37.812477] TTC __clickXmlJobs result: True
[2025-07-14 10:40:37.812976] TTC comment cache reached: 2/2
[2025-07-14 10:40:52.818573] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/nhantien.php
[2025-07-14 10:40:52.961319] Request successful, status: 200
[2025-07-14 10:40:52.962316] TTC getXuJob response: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 10:40:52.962816] TTC getXuJob failed: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 10:40:52.963314] TTC comment xu delay: 2 seconds
[2025-07-14 10:40:54.963973] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:40:55.118698] Request successful, status: 200
[2025-07-14 10:41:12.127025] TTC processing job 3: 7524730653871492359, https://www.tiktok.com/@1thang9_28/video/7524730653871492359
[2025-07-14 10:41:12.128023] TTC comment content found: ["","Ch\u1ea5t serum h\u01a1i l\u1ecfng, thoa l\u00ean m\u00e1t m\u00e1t th\u00edch gh\u00ea.","",""...
[2025-07-14 10:41:12.128524] TTC navigating to: https://www.tiktok.com/@1thang9_28/video/7524730653871492359
[2025-07-14 10:41:16.638910] TTC navigation successful
[2025-07-14 10:41:16.639943] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:41:31.483091] TTC tried Escape key 3 times to close popups
[2025-07-14 10:41:31.508559] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:41:31.508559] TTC starting aggressive popup closing
[2025-07-14 10:41:35.906028] TTC force removed modals with JavaScript
[2025-07-14 10:41:36.423731] TTC popup still visible after all attempts
[2025-07-14 10:41:36.424231] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:41:36.446687] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:41:48.742817] TTC tried Escape key 3 times to close popups
[2025-07-14 10:41:49.464020] TTC comment input clicked with ActionChains (fast)
[2025-07-14 10:41:51.052833] TTC cleaned comment text: 'Chị dễ thương quá.' (length: 18)
[2025-07-14 10:41:51.053333] TTC typing comment: Chị dễ thương quá....
[2025-07-14 10:41:52.593634] TTC typing method 1 result: expected='Chị dễ thương quá.', actual='hị dễ thương quá.'
[2025-07-14 10:41:52.594155] TTC typing method 1 success: 17/18 chars
[2025-07-14 10:41:53.603199] TTC verify attempt 1: expected='Chị dễ thương quá....', actual='hị dễ thương quá....'
[2025-07-14 10:41:54.144331] TTC comment input current value: 'hị dễ thương quá....'
[2025-07-14 10:41:54.144825] TTC comment text verified in input
[2025-07-14 10:41:54.151812] TTC detected missing first character, fixing...
[2025-07-14 10:41:54.987301] TTC final verification passed: 'Chị dễ thương quá....'
[2025-07-14 10:41:55.521294] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:41:55.851812] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:41:57.390752] TTC comment verified in comment list
[2025-07-14 10:41:57.391252] TTC comment posted successfully
[2025-07-14 10:41:57.392250] TTC __clickXmlJobs result: True
[2025-07-14 10:41:57.392750] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:41:57.534487] Request successful, status: 200
[2025-07-14 10:42:14.542401] TTC processing job 4: 7526480464849227013, https://www.tiktok.com/@nguynththutrang439/video/7526480464849227013
[2025-07-14 10:42:14.543388] TTC comment content found: ["g\u1eb7p c vk kia hi\u1ec1n \u0111\u1ea5y. g\u1eb7p tao t\u00e0n canh","\u1ee6a g\u00e1i \u0111\u0...
[2025-07-14 10:42:14.544371] TTC navigating to: https://www.tiktok.com/@nguynththutrang439/video/7526480464849227013
[2025-07-14 10:42:18.798324] TTC navigation successful
[2025-07-14 10:42:18.799321] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:42:33.742491] TTC tried Escape key 3 times to close popups
[2025-07-14 10:42:33.757962] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:42:33.757962] TTC starting aggressive popup closing
[2025-07-14 10:42:37.917094] TTC force removed modals with JavaScript
[2025-07-14 10:42:38.430623] TTC popup still visible after all attempts
[2025-07-14 10:42:38.431122] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:42:38.448090] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:42:40.014130] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:42:40.068524] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:42:40.382652] Request successful, status: 200
[2025-07-14 10:42:40.382931] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:42:40.421375] Request successful, status: 200
[2025-07-14 10:42:40.421857] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:42:40.518673] Request successful, status: 200
[2025-07-14 10:42:40.569093] Request successful, status: 200
[2025-07-14 10:42:40.569285] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:42:41.065638] Request successful, status: 200
[2025-07-14 10:42:41.066636] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:42:41.231827] Request successful, status: 200
[2025-07-14 10:42:50.622560] TTC tried Escape key 3 times to close popups
[2025-07-14 10:42:51.310256] TTC comment input clicked with ActionChains (fast)
[2025-07-14 10:42:52.866832] TTC cleaned comment text: 'k bt  gại hay gì,' (length: 17)
[2025-07-14 10:42:52.867319] TTC typing comment: k bt  gại hay gì,...
[2025-07-14 10:42:54.473790] TTC typing method 1 result: expected='k bt  gại hay gì,', actual='bt  gại hay gì,'
[2025-07-14 10:42:54.476269] TTC typing method 1 success: 15/17 chars
[2025-07-14 10:42:55.485359] TTC verify attempt 1: expected='k bt  gại hay gì,...', actual='bt  gại hay gì,...'
[2025-07-14 10:42:56.015356] TTC comment input current value: ' bt  gại hay gì,...'
[2025-07-14 10:42:56.015855] TTC comment text verified in input
[2025-07-14 10:42:56.021345] TTC detected missing first character, fixing...
[2025-07-14 10:42:56.855767] TTC final verification passed: 'k bt  gại hay gì,...'
[2025-07-14 10:42:57.378776] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:42:57.696176] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:42:59.256226] TTC comment not found in list (may still be processing)
[2025-07-14 10:42:59.256724] TTC comment posted successfully
[2025-07-14 10:42:59.256999] TTC __clickXmlJobs result: True
[2025-07-14 10:42:59.257224] TTC comment cache reached: 2/2
[2025-07-14 10:43:14.261363] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/nhantien.php
[2025-07-14 10:43:14.403077] Request successful, status: 200
[2025-07-14 10:43:14.404073] TTC getXuJob response: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 10:43:14.404570] TTC getXuJob failed: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 10:43:14.405069] TTC comment xu delay: 2 seconds
[2025-07-14 10:43:14.854735] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=ta.chinh.4m.2002&nickchay=ta.chinh.4m.2002
[2025-07-14 10:43:14.870218] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=kyzkuq1103&nickchay=kyzkuq1103
[2025-07-14 10:43:15.313771] Request successful, status: 200
[2025-07-14 10:43:15.638140] Request successful, status: 200
[2025-07-14 10:43:16.405924] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:43:16.585155] Request successful, status: 200
[2025-07-14 10:43:16.702434] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=quan.thanh.4nf.3001&nickchay=quan.thanh.4nf.3001
[2025-07-14 10:43:17.521214] Request successful, status: 200
[2025-07-14 10:43:20.316588] TTC getJob called for type: comment
[2025-07-14 10:43:20.317086] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=77adg1i5h0ii6peuk2vi32d9t3; Path=/'}
[2025-07-14 10:43:20.317585] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:43:20.448821] Request successful, status: 200
[2025-07-14 10:43:20.449339] TTC comment response status: 200
[2025-07-14 10:43:20.449339] TTC comment response text: {"error":"Lấy nhiệm vụ sau 21 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":21}...
[2025-07-14 10:43:20.449836] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 10:43:20.450334] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 21 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 21}
[2025-07-14 10:43:20.641972] TTC getJob called for type: comment
[2025-07-14 10:43:20.641972] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=jtufdt607nq8tpf9tni6oiulr3; Path=/'}
[2025-07-14 10:43:20.642973] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:43:20.819983] Request successful, status: 200
[2025-07-14 10:43:20.820468] TTC comment response status: 200
[2025-07-14 10:43:20.820468] TTC comment response text: [{"idpost":"7522852118768798984","link":"https:\/\/www.tiktok.com\/@doxinhdayneohlala\/video\/7522852118768798984","nd":"[\"\\u2764\\ufe0f\\u2764\\ufe0f\\u2764\\ufe0f\",\"\\ud83d\\udc96\",\"\\ud83e\\udd70\"]"},{"idpost":"7526509194791587090","link":"https:\/\/www.tiktok.com\/@hongvytiktokshop\/video\/7526509194791587090","nd":"[\"C\\u00f3 c\\u1ea7n v\\u1ed1n \\u0111\\u1ec3 l\\u00e0m dropshipping kh\\u00f4ng b\\u1ea1n?\",\"C\\u00f3 kh\\u00f3a h\\u1ecdc dropshipping n\\u00e0o b\\u1ea1n recommend h...
[2025-07-14 10:43:20.821978] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 10:43:20.822485] TTC comment - Jobs list detected, length: 36
[2025-07-14 10:43:20.880369] TTC starting jobs iteration: 36 jobs
[2025-07-14 10:43:20.880877] TTC comment settings: max=12345, cache=2
[2025-07-14 10:43:20.881867] TTC processing job 1: 7522852118768798984, https://www.tiktok.com/@doxinhdayneohlala/video/7522852118768798984
[2025-07-14 10:43:20.882366] TTC comment content found: ["\u2764\ufe0f\u2764\ufe0f\u2764\ufe0f","\ud83d\udc96","\ud83e\udd70"]...
[2025-07-14 10:43:20.889371] TTC navigating to: https://www.tiktok.com/@doxinhdayneohlala/video/7522852118768798984
[2025-07-14 10:43:22.524423] TTC getJob called for type: comment
[2025-07-14 10:43:22.524423] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=lu34t6fej5fh4u82u295mj8715; Path=/'}
[2025-07-14 10:43:22.524922] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:43:22.661680] Request successful, status: 200
[2025-07-14 10:43:22.662181] TTC comment response status: 200
[2025-07-14 10:43:22.662181] TTC comment response text: {"error":"Lấy nhiệm vụ sau 25 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":25}...
[2025-07-14 10:43:22.662678] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 10:43:22.662678] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 25 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 25}
[2025-07-14 10:43:22.849808] TTC navigation successful
[2025-07-14 10:43:22.850307] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:43:33.591264] TTC processing job 5: 7526501035469278472, https://www.tiktok.com/@hanbie_jun/video/7526501035469278472
[2025-07-14 10:43:33.591876] TTC comment content found: ["ai m\u00ea Tsubaki ko th\u1ec3 b\u1ecf qua","ai \u0111i r\u1ed3i review nh\u1eb9 ik","\u0111ang c\...
[2025-07-14 10:43:33.592745] TTC navigating to: https://www.tiktok.com/@hanbie_jun/video/7526501035469278472
[2025-07-14 10:43:35.883926] TTC navigation successful
[2025-07-14 10:43:35.884908] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:43:41.462109] TTC getJob called for type: comment
[2025-07-14 10:43:41.462109] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=77adg1i5h0ii6peuk2vi32d9t3; Path=/'}
[2025-07-14 10:43:41.462607] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:43:41.635796] Request successful, status: 200
[2025-07-14 10:43:41.636796] TTC comment response status: 200
[2025-07-14 10:43:41.636796] TTC comment response text: [{"idpost":"7526602525848440072","link":"https:\/\/www.tiktok.com\/@bang12999\/photo\/7526602525848440072","nd":"[\"Tranh c\\u0169ng c\\u0169ng\",\"\\u0110\\u00e3 t\\u01b0\\u01a1ng t\\u00e1c\",\"Ch\\u00e9o trong ib \\u1ea1\",\"M\\u00ecnh c\\u0169ng tham gia r\\u00f9i \\u00e1\",\"Nh\\u00ecn \\u0111\\u1eb9p qu\\u00e1 tr\\u1eddi\",\"\\u0110\\u1eb9p \\u00e1 b\",\"V\\u1ebd T\\u1ebft \\u0111oan ng\\u1ecd y ch\\u1ebf\",\"Good luck\",\"Tham gia \\u1edf \\u0111\\u00e2u v\\u1eady b\",\"M\\u00ecnh th\\u00e...
[2025-07-14 10:43:41.637792] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 10:43:41.638291] TTC comment - Jobs list detected, length: 36
[2025-07-14 10:43:41.694669] TTC starting jobs iteration: 36 jobs
[2025-07-14 10:43:41.695168] TTC comment settings: max=12345, cache=2
[2025-07-14 10:43:41.695776] TTC processing job 1: 7526602525848440072, https://www.tiktok.com/@bang12999/photo/7526602525848440072
[2025-07-14 10:43:41.696166] TTC comment content found: ["Tranh c\u0169ng c\u0169ng","\u0110\u00e3 t\u01b0\u01a1ng t\u00e1c","Ch\u00e9o trong ib \u1ea1","M\...
[2025-07-14 10:43:41.702671] TTC navigating to: https://www.tiktok.com/@bang12999/photo/7526602525848440072
[2025-07-14 10:43:43.069568] TTC navigation successful
[2025-07-14 10:43:43.070071] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:43:47.676355] TTC getJob called for type: comment
[2025-07-14 10:43:47.676355] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=lu34t6fej5fh4u82u295mj8715; Path=/'}
[2025-07-14 10:43:47.676856] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:43:47.859011] Request successful, status: 200
[2025-07-14 10:43:47.860023] TTC comment response status: 200
[2025-07-14 10:43:47.860023] TTC comment response text: [{"idpost":"7525308476776582408","link":"https:\/\/www.tiktok.com\/@dailykieutrangtiktok\/video\/7525308476776582408","nd":"[\"\\u01af\\u1edbc g\\u00ec ra th\\u1eadt nhi\\u1ec1u \\u0111\\u01a1n nh\\u01b0 c\\u1ee7a ch\\u1ecb \\u1ea1, ch\\u1ecb chia s\\u1ebb b\\u00ed quy\\u1ebft \\u0111i \\u1ea1\",\"rep ib c\\u1ee7a em nh\\u00e9 ch\\u1ecb\",\"H\\u01b0\\u1edbng d\\u1eabn em l\\u00e0m c\\u00e1i n\\u00e0y \\u0111i ch\\u1ecb \\u01a1i\",\"xin v\\u00eda, xin v\\u00eda ra nhi\\u1ec1u \\u0111\\u01a1n gi\\...
[2025-07-14 10:43:47.861025] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 10:43:47.861520] TTC comment - Jobs list detected, length: 36
[2025-07-14 10:43:47.916426] TTC starting jobs iteration: 36 jobs
[2025-07-14 10:43:47.916899] TTC comment settings: max=12345, cache=2
[2025-07-14 10:43:47.917400] TTC processing job 1: 7525308476776582408, https://www.tiktok.com/@dailykieutrangtiktok/video/7525308476776582408
[2025-07-14 10:43:47.917898] TTC comment content found: ["\u01af\u1edbc g\u00ec ra th\u1eadt nhi\u1ec1u \u0111\u01a1n nh\u01b0 c\u1ee7a ch\u1ecb \u1ea1, ch\...
[2025-07-14 10:43:47.931888] TTC navigating to: https://www.tiktok.com/@dailykieutrangtiktok/video/7525308476776582408
[2025-07-14 10:43:51.778107] TTC tried Escape key 3 times to close popups
[2025-07-14 10:43:51.802551] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:43:51.803068] TTC starting aggressive popup closing
[2025-07-14 10:43:52.634478] TTC navigation successful
[2025-07-14 10:43:52.635009] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:43:56.157576] TTC force removed modals with JavaScript
[2025-07-14 10:43:56.672603] TTC popup still visible after all attempts
[2025-07-14 10:43:56.673108] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:43:56.684564] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:44:08.856655] TTC tried Escape key 3 times to close popups
[2025-07-14 10:44:09.590750] TTC comment input clicked with ActionChains (fast)
[2025-07-14 10:44:11.225658] TTC cleaned comment text: 'ai mê Tsubaki ko thể bỏ qua' (length: 27)
[2025-07-14 10:44:11.226177] TTC typing comment: ai mê Tsubaki ko thể bỏ qua...
[2025-07-14 10:44:13.121073] TTC typing method 1 result: expected='ai mê Tsubaki ko thể bỏ qua', actual='i mê Tsubaki ko thể bỏ qua'
[2025-07-14 10:44:13.121589] TTC typing method 1 success: 26/27 chars
[2025-07-14 10:44:14.129793] TTC verify attempt 1: expected='ai mê Tsubaki ko thể bỏ qua...', actual='i mê Tsubaki ko thể bỏ qua...'
[2025-07-14 10:44:14.674224] TTC comment input current value: 'i mê Tsubaki ko thể bỏ qua...'
[2025-07-14 10:44:14.674722] TTC comment text verified in input
[2025-07-14 10:44:14.683190] TTC detected missing first character, fixing...
[2025-07-14 10:44:15.525594] TTC final verification passed: 'ai mê Tsubaki ko thể bỏ qua...'
[2025-07-14 10:44:16.060086] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:44:16.380478] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:44:17.930571] TTC comment not found in list (may still be processing)
[2025-07-14 10:44:17.931063] TTC comment posted successfully
[2025-07-14 10:44:17.931574] TTC __clickXmlJobs result: True
[2025-07-14 10:44:17.932063] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:44:18.092244] Request successful, status: 200
[2025-07-14 10:44:35.098994] TTC processing job 6: 7526508272870804743, https://www.tiktok.com/@hangtiktokshop_90/video/7526508272870804743
[2025-07-14 10:44:35.099992] TTC comment content found: ["em mu\u1ed1n \u0111\u01b0\u1ee3c t\u01b0 v\u1ea5n \u1ea1","ch\u00fac m\u1eebng ch\u1ecb nh\u00e9, ...
[2025-07-14 10:44:35.100492] TTC navigating to: https://www.tiktok.com/@hangtiktokshop_90/video/7526508272870804743
[2025-07-14 10:44:36.692461] TTC navigation successful
[2025-07-14 10:44:36.692961] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:44:52.141326] TTC tried Escape key 3 times to close popups
[2025-07-14 10:44:52.167259] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:44:52.168259] TTC starting aggressive popup closing
[2025-07-14 10:44:56.585757] TTC force removed modals with JavaScript
[2025-07-14 10:44:57.103274] TTC popup still visible after all attempts
[2025-07-14 10:44:57.103777] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:44:57.116734] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:45:09.329069] TTC tried Escape key 3 times to close popups
[2025-07-14 10:45:10.087120] TTC comment input clicked with ActionChains (fast)
[2025-07-14 10:45:11.675729] TTC cleaned comment text: 'e nhắn tin rồi, c rep ib của e với ạ' (length: 36)
[2025-07-14 10:45:11.676228] TTC typing comment: e nhắn tin rồi, c rep ib của e với ạ...
[2025-07-14 10:45:12.796113] TTC tried Escape key 3 times to close popups
[2025-07-14 10:45:12.822080] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:45:12.822582] TTC starting aggressive popup closing
[2025-07-14 10:45:13.809168] TTC typing method 1 result: expected='e nhắn tin rồi, c rep ib của e với ạ', actual='nhắn tin rồi, c rep ib của e với ạ'
[2025-07-14 10:45:13.809669] TTC typing method 1 success: 34/36 chars
[2025-07-14 10:45:14.816999] TTC verify attempt 1: expected='e nhắn tin rồi, c rep ib của e...', actual='nhắn tin rồi, c rep ib của e v...'
[2025-07-14 10:45:15.352117] TTC comment input current value: ' nhắn tin rồi, c rep ib của e với ạ...'
[2025-07-14 10:45:15.352617] TTC comment text verified in input
[2025-07-14 10:45:15.357607] TTC detected missing first character, fixing...
[2025-07-14 10:45:16.192542] TTC final verification passed: 'e nhắn tin rồi, c rep ib của e với ạ...'
[2025-07-14 10:45:16.724003] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:45:17.055875] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:45:18.615186] TTC comment not found in list (may still be processing)
[2025-07-14 10:45:18.615688] TTC comment posted successfully
[2025-07-14 10:45:18.616191] TTC __clickXmlJobs result: True
[2025-07-14 10:45:18.616191] TTC comment cache reached: 2/2
[2025-07-14 10:45:32.544545] TTC tried Escape key 3 times to close popups
[2025-07-14 10:45:33.624043] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/nhantien.php
[2025-07-14 10:45:33.776408] Request successful, status: 200
[2025-07-14 10:45:33.777406] TTC getXuJob response: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 10:45:33.778406] TTC getXuJob failed: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 10:45:33.778906] TTC comment xu delay: 2 seconds
[2025-07-14 10:45:35.779322] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:45:35.915090] Request successful, status: 200
[2025-07-14 10:45:39.638937] TTC force removed modals with JavaScript
[2025-07-14 10:45:40.155959] TTC popup still visible after all attempts
[2025-07-14 10:45:40.156442] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:45:40.176904] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:45:42.769561] TTC tried Escape key 3 times to close popups
[2025-07-14 10:45:42.792021] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:45:42.792517] TTC starting aggressive popup closing
[2025-07-14 10:45:52.922327] TTC processing job 7: 7524996330486320392, https://www.tiktok.com/@suxinh1010/video/7524996330486320392
[2025-07-14 10:45:52.923328] TTC comment content found: ["\u2764\ufe0f","\u2764\ufe0f","\u2764\ufe0f","\u2764\ufe0f","\u2764\ufe0f","\u2764\ufe0f","\u2764\u...
[2025-07-14 10:45:52.923814] TTC navigating to: https://www.tiktok.com/@suxinh1010/video/7524996330486320392
[2025-07-14 10:45:58.195471] TTC navigation successful
[2025-07-14 10:45:58.195860] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:46:02.786309] TTC detected popup by selector: div[class*="Modal"]
[2025-07-14 10:46:02.786804] TTC starting aggressive popup closing
[2025-07-14 10:46:09.623493] TTC force removed modals with JavaScript
[2025-07-14 10:46:10.140515] TTC popup still visible after all attempts
[2025-07-14 10:46:10.141014] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:46:10.163471] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:46:14.472514] TTC tried Escape key 3 times to close popups
[2025-07-14 10:46:14.499985] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:46:14.500481] TTC starting aggressive popup closing
[2025-07-14 10:46:20.156311] TTC force removed modals with JavaScript
[2025-07-14 10:46:20.673329] TTC popup still visible after all attempts
[2025-07-14 10:46:20.673812] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:46:20.695271] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:46:29.569926] TTC force removed modals with JavaScript
[2025-07-14 10:46:33.007653] TTC tried Escape key 3 times to close popups
[2025-07-14 10:46:33.706331] TTC comment input clicked with ActionChains (fast)
[2025-07-14 10:46:35.265881] TTC cleaned comment text: 'Nice video!' (length: 11)
[2025-07-14 10:46:35.266383] TTC typing comment: Nice video!...
[2025-07-14 10:46:36.723631] TTC typing method 1 result: expected='Nice video!', actual='ice video!'
[2025-07-14 10:46:36.724126] TTC typing method 1 success: 10/11 chars
[2025-07-14 10:46:37.732221] TTC verify attempt 1: expected='Nice video!...', actual='ice video!...'
[2025-07-14 10:46:38.272199] TTC comment input current value: 'ice video!...'
[2025-07-14 10:46:38.272697] TTC comment text verified in input
[2025-07-14 10:46:38.279167] TTC detected missing first character, fixing...
[2025-07-14 10:46:39.167784] TTC final verification passed: 'Nice video!...'
[2025-07-14 10:46:39.700774] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:46:40.065568] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:46:41.626135] TTC comment not found in list (may still be processing)
[2025-07-14 10:46:41.626135] TTC comment posted successfully
[2025-07-14 10:46:41.626635] TTC __clickXmlJobs result: True
[2025-07-14 10:46:41.627137] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:46:41.774356] Request successful, status: 200
[2025-07-14 10:53:45.585469] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 10:53:45.585966] TTC auto-enabled minisize with scale factor: 0.65
[2025-07-14 10:53:47.344414] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 10:53:47.573500] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 10:53:55.975495] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 10:53:55.975995] TTC auto-enabled minisize with scale factor: 0.65
[2025-07-14 10:53:57.611543] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 10:53:57.725329] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 10:54:03.986068] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:54:04.356506] Request successful, status: 200
[2025-07-14 10:54:04.357004] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:54:04.508218] Request successful, status: 200
[2025-07-14 10:54:19.259632] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 10:54:20.117510] Request successful, status: 200
[2025-07-14 10:54:25.119993] TTC getJob called for type: comment
[2025-07-14 10:54:25.119993] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=uo3ev3kssklgfchabpeam7v7a3; Path=/'}
[2025-07-14 10:54:25.120982] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:54:25.278470] Request successful, status: 200
[2025-07-14 10:54:25.278470] TTC comment response status: 200
[2025-07-14 10:54:25.278470] TTC comment response text: [{"idpost":"7526748993615826183","link":"https:\/\/www.tiktok.com\/@thuong.tay8386\/video\/7526748993615826183","nd":"[\"\\u00e1nh m\\u1eaft cu\\u1ed1n h\\u00fat\",\"c\\u01b0\\u1eddi l\\u00e0 tan ch\\u1ea3y\",\"tay \\u0111\\u1eb9p qu\\u00e1 tr\\u1eddi\",\"\\u00e1o \\u0111en h\\u1ee3p l\\u1eafm\",\"n\\u00e9t \\u0111\\u1eb9p d\\u1ecbu d\\u00e0ng\",\"g\\u00f3c nghi\\u00eang \\u0111\\u1eb9p l\\u1eafm\",\"r\\u1ea5t c\\u00f3 duy\\u00ean lu\\u00f4n\",\"phong c\\u00e1ch nh\\u1eb9 nh\\u00e0ng\",\".\",\"d...
[2025-07-14 10:54:25.279469] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 10:54:25.279469] TTC comment - Jobs list detected, length: 36
[2025-07-14 10:54:25.325108] TTC starting jobs iteration: 36 jobs
[2025-07-14 10:54:25.325385] TTC comment settings: max=12345, cache=2
[2025-07-14 10:54:25.326382] TTC processing job 1: 7526748993615826183, https://www.tiktok.com/@thuong.tay8386/video/7526748993615826183
[2025-07-14 10:54:25.326898] TTC comment content found: ["\u00e1nh m\u1eaft cu\u1ed1n h\u00fat","c\u01b0\u1eddi l\u00e0 tan ch\u1ea3y","tay \u0111\u1eb9p qu...
[2025-07-14 10:54:25.327878] TTC navigating to: https://www.tiktok.com/@thuong.tay8386/video/7526748993615826183
[2025-07-14 10:54:28.914598] TTC navigation successful
[2025-07-14 10:54:28.915096] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:54:44.189900] TTC tried Escape key 3 times to close popups
[2025-07-14 10:54:44.213352] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:54:44.213352] TTC starting aggressive popup closing
[2025-07-14 10:54:48.371498] TTC force removed modals with JavaScript
[2025-07-14 10:54:48.911469] TTC popup still visible after all attempts
[2025-07-14 10:54:48.911970] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:54:48.929936] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:55:01.087279] TTC tried Escape key 3 times to close popups
[2025-07-14 10:55:01.816883] TTC comment input clicked with ActionChains (normal)
[2025-07-14 10:55:03.374802] TTC cleaned comment text: 'fan cứng luôn rồi' (length: 17)
[2025-07-14 10:55:03.374802] TTC typing comment: fan cứng luôn rồi...
[2025-07-14 10:55:04.816981] TTC typing method 1 result: expected='fan cứng luôn rồi', actual='an cứng luôn rồi'
[2025-07-14 10:55:04.817482] TTC typing method 1 success: 16/17 chars
[2025-07-14 10:55:05.825576] TTC verify attempt 1: expected='fan cứng luôn rồi...', actual='an cứng luôn rồi...'
[2025-07-14 10:55:06.373591] TTC comment input current value: 'an cứng luôn rồi...'
[2025-07-14 10:55:06.374089] TTC comment text verified in input
[2025-07-14 10:55:06.381577] TTC detected missing first character, fixing...
[2025-07-14 10:55:07.218992] TTC final verification passed: 'fan cứng luôn rồi...'
[2025-07-14 10:55:07.751571] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:55:08.116882] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:55:09.676347] TTC comment not found in list (may still be processing)
[2025-07-14 10:55:09.676846] TTC comment posted successfully
[2025-07-14 10:55:09.677323] TTC __clickXmlJobs result: True
[2025-07-14 10:55:09.677323] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:55:09.822049] Request successful, status: 200
[2025-07-14 10:55:26.829081] TTC processing job 2: 7524713792303844626, https://www.tiktok.com/@hdv_binhvinhhy/video/7524713792303844626
[2025-07-14 10:55:26.829602] TTC comment content found: [".","\ud83c\uddfb\ud83c\uddf3\ud83c\uddfb\ud83c\uddf3","Hi anh","\u2705\u2705","Nh\u1ea1c hay","\ud...
[2025-07-14 10:55:26.830596] TTC navigating to: https://www.tiktok.com/@hdv_binhvinhhy/video/7524713792303844626
[2025-07-14 10:55:31.695509] TTC navigation successful
[2025-07-14 10:55:31.696034] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:55:47.479190] TTC tried Escape key 3 times to close popups
[2025-07-14 10:55:47.505158] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 10:55:47.505657] TTC starting aggressive popup closing
[2025-07-14 10:55:51.869142] TTC force removed modals with JavaScript
[2025-07-14 10:55:52.387423] TTC popup still visible after all attempts
[2025-07-14 10:55:52.387922] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:55:52.413873] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:56:04.768496] TTC tried Escape key 3 times to close popups
[2025-07-14 10:56:05.632404] TTC comment input clicked with ActionChains (normal)
[2025-07-14 10:56:07.261427] TTC cleaned comment text: '✅✅' (length: 2)
[2025-07-14 10:56:07.261910] TTC typing comment: ✅✅...
[2025-07-14 10:56:08.342367] TTC typing method 1 result: expected='✅✅', actual='✅'
[2025-07-14 10:56:08.343366] TTC typing method 1 insufficient content: 1/2 chars
[2025-07-14 10:56:08.343865] TTC trying method 2: character by character
[2025-07-14 10:56:10.509272] TTC method 2 result: expected='✅✅', actual='✅✅'
[2025-07-14 10:56:10.509771] TTC typing method 2 success: 2/2 chars
[2025-07-14 10:56:11.519879] TTC verify attempt 1: expected='✅✅...', actual='✅✅...'
[2025-07-14 10:56:12.078805] TTC comment input current value: '✅✅...'
[2025-07-14 10:56:12.079303] TTC comment text verified in input
[2025-07-14 10:56:12.083297] TTC final verification failed, content too short: '✅✅'
[2025-07-14 10:56:12.083297] TTC trying selector: div[data-e2e="comment-input"] div[role="textbox"]
[2025-07-14 10:56:12.095274] TTC found comment input with selector: div[data-e2e="comment-input"] div[role="textbox"]
[2025-07-14 10:56:24.292121] TTC tried Escape key 3 times to close popups
[2025-07-14 10:56:24.968344] TTC comment input clicked with ActionChains (normal)
[2025-07-14 10:56:26.497777] TTC cleaned comment text: '✅✅' (length: 2)
[2025-07-14 10:56:26.498277] TTC typing comment: ✅✅...
[2025-07-14 10:56:27.553086] TTC typing method 1 result: expected='✅✅', actual='✅✅'
[2025-07-14 10:56:27.553616] TTC typing method 1 success: 2/2 chars
[2025-07-14 10:56:28.561878] TTC verify attempt 1: expected='✅✅...', actual='✅✅...'
[2025-07-14 10:56:29.086887] TTC comment input current value: '✅✅...'
[2025-07-14 10:56:29.087384] TTC comment text verified in input
[2025-07-14 10:56:29.093875] TTC final verification failed, content too short: '✅✅'
[2025-07-14 10:56:29.093875] TTC trying selector: div[data-e2e="comment-input"]
[2025-07-14 10:56:29.108829] TTC found comment input with selector: div[data-e2e="comment-input"]
[2025-07-14 10:56:41.294401] TTC tried Escape key 3 times to close popups
[2025-07-14 10:56:41.328337] TTC found child input: div[contenteditable="true"]
[2025-07-14 10:56:41.999552] TTC comment input clicked with ActionChains (normal)
[2025-07-14 10:56:43.555628] TTC cleaned comment text: '✅✅' (length: 2)
[2025-07-14 10:56:43.556127] TTC typing comment: ✅✅...
[2025-07-14 10:56:44.653536] TTC typing method 1 result: expected='✅✅', actual='✅✅'
[2025-07-14 10:56:44.654035] TTC typing method 1 success: 2/2 chars
[2025-07-14 10:56:45.659135] TTC verify attempt 1: expected='✅✅...', actual='✅✅...'
[2025-07-14 10:56:46.176657] TTC comment input current value: '✅✅...'
[2025-07-14 10:56:46.177155] TTC comment text verified in input
[2025-07-14 10:56:46.182146] TTC final verification failed, content too short: '✅✅'
[2025-07-14 10:56:46.182146] TTC trying selector: div[data-e2e="comment-input-container"] div[contenteditable="true"]
[2025-07-14 10:56:46.700665] TTC trying selector: div[data-e2e="comment-input-container"] div[role="textbox"]
[2025-07-14 10:56:47.223180] TTC trying selector: div[data-e2e="comment-input-container"]
[2025-07-14 10:56:47.748686] TTC trying selector: div[role="textbox"][contenteditable="true"]
[2025-07-14 10:56:47.764656] TTC found comment input with selector: div[role="textbox"][contenteditable="true"]
[2025-07-14 10:57:00.015166] TTC tried Escape key 3 times to close popups
[2025-07-14 10:57:00.685402] TTC comment input clicked with ActionChains (normal)
[2025-07-14 10:57:02.265661] TTC cleaned comment text: '✅✅' (length: 2)
[2025-07-14 10:57:02.266159] TTC typing comment: ✅✅...
[2025-07-14 10:57:03.348505] TTC typing method 1 result: expected='✅✅', actual='✅✅'
[2025-07-14 10:57:03.348505] TTC typing method 1 success: 2/2 chars
[2025-07-14 10:57:04.354586] TTC verify attempt 1: expected='✅✅...', actual='✅✅...'
[2025-07-14 10:57:04.869114] TTC comment input current value: '✅✅...'
[2025-07-14 10:57:04.869614] TTC comment text verified in input
[2025-07-14 10:57:04.874104] TTC final verification failed, content too short: '✅✅'
[2025-07-14 10:57:04.874604] TTC trying selector: div[contenteditable="true"][data-placeholder*="comment"]
[2025-07-14 10:57:05.402123] TTC trying selector: div[contenteditable="true"][data-placeholder*="Comment"]
[2025-07-14 10:57:05.935117] TTC trying selector: div[contenteditable="true"][placeholder*="comment"]
[2025-07-14 10:57:06.466114] TTC trying selector: div[contenteditable="true"][placeholder*="Comment"]
[2025-07-14 10:57:06.999106] TTC trying selector: div[contenteditable="true"]
[2025-07-14 10:57:07.017570] TTC found comment input with selector: div[contenteditable="true"]
[2025-07-14 10:57:19.209139] TTC tried Escape key 3 times to close popups
[2025-07-14 10:57:19.888836] TTC comment input clicked with ActionChains (normal)
[2025-07-14 10:57:21.419944] TTC cleaned comment text: '✅✅' (length: 2)
[2025-07-14 10:57:21.420462] TTC typing comment: ✅✅...
[2025-07-14 10:57:22.499923] TTC typing method 1 result: expected='✅✅', actual='✅✅'
[2025-07-14 10:57:22.500422] TTC typing method 1 success: 2/2 chars
[2025-07-14 10:57:23.507519] TTC verify attempt 1: expected='✅✅...', actual='✅✅...'
[2025-07-14 10:57:24.030529] TTC comment input current value: '✅✅...'
[2025-07-14 10:57:24.031527] TTC comment text verified in input
[2025-07-14 10:57:24.038015] TTC final verification failed, content too short: '✅✅'
[2025-07-14 10:57:24.038514] TTC trying selector: [contenteditable="true"]
[2025-07-14 10:57:24.055465] TTC found comment input with selector: [contenteditable="true"]
[2025-07-14 10:57:44.439495] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 10:57:44.439973] TTC auto-enabled minisize with scale factor: 0.5
[2025-07-14 10:57:46.220216] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 10:57:46.325017] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 10:57:52.540791] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:57:52.938038] Request successful, status: 200
[2025-07-14 10:57:52.938538] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:57:53.086760] Request successful, status: 200
[2025-07-14 10:57:57.426196] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 10:57:57.426696] TTC auto-enabled minisize with scale factor: 0.5
[2025-07-14 10:57:59.076904] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 10:57:59.182219] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 10:58:05.739827] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:58:06.111108] Request successful, status: 200
[2025-07-14 10:58:06.111746] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:58:06.249366] Request successful, status: 200
[2025-07-14 10:58:21.771214] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 10:58:22.613954] Request successful, status: 200
[2025-07-14 11:01:10.805088] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:01:10.805593] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:01:12.462804] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:01:12.576578] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:01:19.428492] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 11:01:19.772294] Request successful, status: 200
[2025-07-14 11:01:19.772791] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 11:01:19.924510] Request successful, status: 200
[2025-07-14 11:01:34.835570] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 11:01:35.856056] Request successful, status: 200
[2025-07-14 11:01:40.858930] TTC getJob called for type: comment
[2025-07-14 11:01:40.859429] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=lptr9nlo0ntusoc4hob4fmoc37; Path=/'}
[2025-07-14 11:01:40.859910] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 11:01:41.059019] Request successful, status: 200
[2025-07-14 11:01:41.059518] TTC comment response status: 200
[2025-07-14 11:01:41.059518] TTC comment response text: {"error":"Lấy nhiệm vụ sau 43 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":43}...
[2025-07-14 11:01:41.060036] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 11:01:41.060531] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 43 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 43}
[2025-07-14 11:02:24.078871] TTC getJob called for type: comment
[2025-07-14 11:02:24.078871] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=lptr9nlo0ntusoc4hob4fmoc37; Path=/'}
[2025-07-14 11:02:24.079869] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 11:02:24.240074] Request successful, status: 200
[2025-07-14 11:02:24.241056] TTC comment response status: 200
[2025-07-14 11:02:24.241056] TTC comment response text: [{"idpost":"7525298654626827528","link":"https:\/\/www.tiktok.com\/@yeuanhnhucutvoiboncau\/photo\/7525298654626827528","nd":"[\"m\\u1ed7i d\\u00f2ng 1 n\\u1ed9i dung k\\u00e8m icon\"]"},{"idpost":"7526636344890707208","link":"https:\/\/www.tiktok.com\/@user016052009\/video\/7526636344890707208","nd":"[\"ch\\u00e9o \\u1ea1\"]"},{"idpost":"7525709083211500821","link":"https:\/\/www.tiktok.com\/@meo.lcc\/video\/7525709083211500821","nd":"[\"\\ud83d\\ude34\\ud83d\\ude34\\ud83d\\ude34\\ud83d\\ude34\"...
[2025-07-14 11:02:24.242052] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 11:02:24.242571] TTC comment - Jobs list detected, length: 36
[2025-07-14 11:02:24.287482] TTC starting jobs iteration: 36 jobs
[2025-07-14 11:02:24.287482] TTC comment settings: max=12345, cache=2
[2025-07-14 11:02:24.288481] TTC processing job 1: 7525298654626827528, https://www.tiktok.com/@yeuanhnhucutvoiboncau/photo/7525298654626827528
[2025-07-14 11:02:24.288980] TTC comment content found: ["m\u1ed7i d\u00f2ng 1 n\u1ed9i dung k\u00e8m icon"]...
[2025-07-14 11:02:24.289479] TTC navigating to: https://www.tiktok.com/@yeuanhnhucutvoiboncau/photo/7525298654626827528
[2025-07-14 11:02:25.986792] TTC navigation successful
[2025-07-14 11:02:25.987290] TTC calling __clickXmlJobs with comment: True
[2025-07-14 11:02:41.853791] TTC tried Escape key 3 times to close popups
[2025-07-14 11:02:43.156243] TTC special operation detected: openChrome, minisize: False
[2025-07-14 11:02:43.157259] TTC minisize disabled for openChrome/getCookie operation
[2025-07-14 11:02:44.788947] TTC window sizing for special operation: openChrome, minisize: False
[2025-07-14 11:02:44.919210] TTC normal size for openChrome/getCookie
[2025-07-14 11:02:45.084871] TTC detected popup by selector: div[class*="Modal"]
[2025-07-14 11:02:45.085387] TTC starting aggressive popup closing
[2025-07-14 11:02:49.263708] TTC force removed modals with JavaScript
[2025-07-14 11:02:51.876069] TTC successfully closed keyboard shortcuts popup
[2025-07-14 11:02:51.876589] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:02:51.898545] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:03:04.058710] TTC tried Escape key 3 times to close popups
[2025-07-14 11:03:04.746834] TTC comment input clicked with ActionChains (normal)
[2025-07-14 11:03:06.317572] TTC cleaned comment text: 'mỗi dòng 1 nội dung kèm icon' (length: 28)
[2025-07-14 11:03:06.318073] TTC typing comment: mỗi dòng 1 nội dung kèm icon...
[2025-07-14 11:03:07.912943] TTC typing method 1 result: expected='mỗi dòng 1 nội dung kèm icon', actual='ỗi dòng 1 nội dung kèm icon'
[2025-07-14 11:03:07.913424] TTC typing method 1 success: 27/28 chars
[2025-07-14 11:03:08.922439] TTC verify attempt 1: expected='mỗi dòng 1 nội dung kèm icon...', actual='ỗi dòng 1 nội dung kèm icon...'
[2025-07-14 11:03:09.467453] TTC comment input current value: 'ỗi dòng 1 nội dung kèm icon...'
[2025-07-14 11:03:09.467956] TTC comment text verified in input
[2025-07-14 11:03:09.475440] TTC detected missing first character, fixing...
[2025-07-14 11:03:10.326780] TTC final verification passed: 'mỗi dòng 1 nội dung kèm icon...'
[2025-07-14 11:03:10.860010] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 11:03:11.203841] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 11:03:12.771741] TTC comment not found in list (may still be processing)
[2025-07-14 11:03:12.772235] TTC comment posted successfully
[2025-07-14 11:03:12.773234] TTC __clickXmlJobs result: True
[2025-07-14 11:03:12.773734] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 11:03:12.929001] Request successful, status: 200
[2025-07-14 11:03:29.934635] TTC processing job 2: 7526636344890707208, https://www.tiktok.com/@user016052009/video/7526636344890707208
[2025-07-14 11:03:29.935653] TTC comment content found: ["ch\u00e9o \u1ea1"]...
[2025-07-14 11:03:29.936649] TTC navigating to: https://www.tiktok.com/@user016052009/video/7526636344890707208
[2025-07-14 11:03:34.685463] TTC navigation successful
[2025-07-14 11:03:34.685920] TTC calling __clickXmlJobs with comment: True
[2025-07-14 11:03:49.312145] TTC tried Escape key 3 times to close popups
[2025-07-14 11:03:49.339079] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 11:03:49.339594] TTC starting aggressive popup closing
[2025-07-14 11:03:53.755682] TTC force removed modals with JavaScript
[2025-07-14 11:03:54.310604] TTC popup still visible after all attempts
[2025-07-14 11:03:54.311105] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:03:54.332448] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:04:06.455728] TTC tried Escape key 3 times to close popups
[2025-07-14 11:04:07.156399] TTC comment input clicked with ActionChains (normal)
[2025-07-14 11:04:08.763278] TTC cleaned comment text: 'chéo ạ' (length: 6)
[2025-07-14 11:04:08.763764] TTC typing comment: chéo ạ...
[2025-07-14 11:04:10.058273] TTC typing method 1 result: expected='chéo ạ', actual='héo ạ'
[2025-07-14 11:04:10.058772] TTC typing method 1 success: 5/6 chars
[2025-07-14 11:04:11.068802] TTC verify attempt 1: expected='chéo ạ...', actual='héo ạ...'
[2025-07-14 11:04:11.602269] TTC comment input current value: 'héo ạ...'
[2025-07-14 11:04:11.602269] TTC comment text verified in input
[2025-07-14 11:04:11.617737] TTC detected missing first character, fixing...
[2025-07-14 11:04:12.451623] TTC final verification passed: 'chéo ạ...'
[2025-07-14 11:04:12.977109] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 11:04:13.312458] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 11:04:14.866453] TTC comment not found in list (may still be processing)
[2025-07-14 11:04:14.866969] TTC comment posted successfully
[2025-07-14 11:04:14.867467] TTC __clickXmlJobs result: True
[2025-07-14 11:04:14.867949] TTC comment cache reached: 2/2
[2025-07-14 11:04:29.923342] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/nhantien.php
[2025-07-14 11:04:30.160385] Request successful, status: 200
[2025-07-14 11:04:30.161383] TTC getXuJob response: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 11:04:30.161883] TTC getXuJob failed: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 11:04:30.162383] TTC comment xu delay: 2 seconds
[2025-07-14 11:04:32.162541] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 11:04:32.303280] Request successful, status: 200
[2025-07-14 11:04:49.309287] TTC processing job 3: 7525709083211500821, https://www.tiktok.com/@meo.lcc/video/7525709083211500821
[2025-07-14 11:04:49.310284] TTC comment content found: ["\ud83d\ude34\ud83d\ude34\ud83d\ude34\ud83d\ude34","\u2764\ufe0f\u2764\ufe0f","Ng\u01b0\u1ee1ng m\u...
[2025-07-14 11:04:49.311267] TTC navigating to: https://www.tiktok.com/@meo.lcc/video/7525709083211500821
[2025-07-14 11:04:53.574863] TTC navigation successful
[2025-07-14 11:04:53.575378] TTC calling __clickXmlJobs with comment: True
[2025-07-14 11:05:08.379483] TTC tried Escape key 3 times to close popups
[2025-07-14 11:05:11.634238] TTC detected popup by selector: div[class*="Modal"]
[2025-07-14 11:05:11.634738] TTC starting aggressive popup closing
[2025-07-14 11:05:16.077608] TTC force removed modals with JavaScript
[2025-07-14 11:05:18.703884] TTC successfully closed keyboard shortcuts popup
[2025-07-14 11:05:18.704380] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:05:18.726323] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:05:30.871064] TTC tried Escape key 3 times to close popups
[2025-07-14 11:05:31.657968] TTC comment input clicked with ActionChains (normal)
[2025-07-14 11:05:33.233439] TTC cleaned comment text: '♥♥' (length: 2)
[2025-07-14 11:05:33.233938] TTC typing comment: ♥♥...
[2025-07-14 11:05:34.294419] TTC typing method 1 result: expected='♥♥', actual='♥'
[2025-07-14 11:05:34.294917] TTC typing method 1 insufficient content: 1/2 chars
[2025-07-14 11:05:34.295418] TTC trying method 2: character by character
[2025-07-14 11:05:36.447279] TTC method 2 result: expected='♥♥', actual='♥♥'
[2025-07-14 11:05:36.447783] TTC typing method 2 success: 2/2 chars
[2025-07-14 11:05:37.455604] TTC verify attempt 1: expected='♥♥...', actual='♥♥...'
[2025-07-14 11:05:37.989579] TTC comment input current value: '♥♥...'
[2025-07-14 11:05:37.990060] TTC comment text verified in input
[2025-07-14 11:05:37.997061] TTC final verification failed, content too short: '♥♥'
[2025-07-14 11:05:37.997061] TTC trying selector: div[data-e2e="comment-input"] div[role="textbox"]
[2025-07-14 11:05:38.011038] TTC found comment input with selector: div[data-e2e="comment-input"] div[role="textbox"]
[2025-07-14 11:05:50.238840] TTC tried Escape key 3 times to close popups
[2025-07-14 11:05:50.898547] TTC comment input clicked with ActionChains (normal)
[2025-07-14 11:05:52.461771] TTC cleaned comment text: '♥♥' (length: 2)
[2025-07-14 11:05:52.462280] TTC typing comment: ♥♥...
[2025-07-14 11:05:53.586207] TTC typing method 1 result: expected='♥♥', actual='♥♥'
[2025-07-14 11:05:53.587188] TTC typing method 1 success: 2/2 chars
[2025-07-14 11:05:54.595268] TTC verify attempt 1: expected='♥♥...', actual='♥♥...'
[2025-07-14 11:05:55.119745] TTC comment input current value: '♥♥...'
[2025-07-14 11:05:55.120244] TTC comment text verified in input
[2025-07-14 11:05:55.126731] TTC final verification failed, content too short: '♥♥'
[2025-07-14 11:05:55.127231] TTC trying selector: div[data-e2e="comment-input"]
[2025-07-14 11:05:55.141203] TTC found comment input with selector: div[data-e2e="comment-input"]
[2025-07-14 11:06:19.332119] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:06:19.333117] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:06:20.960478] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:06:21.073781] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:06:27.595269] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 11:06:27.870896] Request successful, status: 200
[2025-07-14 11:06:27.871263] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 11:06:28.012492] Request successful, status: 200
[2025-07-14 11:06:32.589744] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:06:32.590242] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:06:34.367338] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:06:34.473633] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:06:44.512135] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:06:44.513633] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:06:44.747185] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:06:44.747685] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:06:45.021663] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:06:45.022678] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:06:47.769540] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:06:47.785991] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:06:47.788484] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:06:47.889808] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:06:47.899289] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:06:47.902783] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:06:57.099529] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:06:57.100541] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:06:57.379962] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:06:57.380476] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:06:57.675293] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:06:57.675793] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:06:58.735262] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:06:58.844054] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:06:58.942865] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:06:59.051177] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:06:59.189893] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:06:59.298684] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:07:08.299049] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:07:08.299539] TTC auto-enabled minisize with scale factor: 0.3
[2025-07-14 11:07:09.899284] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:07:10.010571] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:07:18.056985] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 11:07:18.348429] Request successful, status: 200
[2025-07-14 11:07:18.349409] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 11:07:18.531081] Request successful, status: 200
[2025-07-14 11:07:51.698825] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 11:07:52.663979] Request successful, status: 200
[2025-07-14 11:07:57.667282] TTC getJob called for type: comment
[2025-07-14 11:07:57.667282] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=13aq8hcdq5vfc3er4t0min46u2; Path=/'}
[2025-07-14 11:07:57.667781] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 11:07:57.820509] Request successful, status: 200
[2025-07-14 11:07:57.821006] TTC comment response status: 200
[2025-07-14 11:07:57.821006] TTC comment response text: [{"idpost":"7526382605126651144","link":"https:\/\/www.tiktok.com\/@dihenhocungthachthao\/video\/7526382605126651144","nd":"[\"Ai m\\u00ea tr\\u00e0 s\\u1eefa \\u0111au r\\u1ed3i v\\u00e0o ngay n\\u00e8o \\ud83d\\ude0d\\ud83e\\udd70\",\"U\\u1ea7y u\\u1ed1ng v\\u00e0o ch\\u1ec9 c\\u00f3 nghi\\u1ec7n\",\"N\\u00e0y c\\u00f3 b\\u00e9o l\\u1eafm kh\\u00f4ng ch\\u1ecb\",\"Hihi th\\u00edch th\\u1eadt\",\"Ngon th\\u1eadt nha\",\"L\\u1eadp team \\u0111i u\\u1ed1ng katinat \\u1edf \\u0111\\u00e0 n\\u1eb5n...
[2025-07-14 11:07:57.822517] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 11:07:57.823005] TTC comment - Jobs list detected, length: 36
[2025-07-14 11:07:57.866901] TTC starting jobs iteration: 36 jobs
[2025-07-14 11:07:57.867419] TTC comment settings: max=12345, cache=2
[2025-07-14 11:07:57.867419] TTC processing job 1: 7526382605126651144, https://www.tiktok.com/@dihenhocungthachthao/video/7526382605126651144
[2025-07-14 11:07:57.867921] TTC comment content found: ["Ai m\u00ea tr\u00e0 s\u1eefa \u0111au r\u1ed3i v\u00e0o ngay n\u00e8o \ud83d\ude0d\ud83e\udd70","U...
[2025-07-14 11:07:57.869899] TTC navigating to: https://www.tiktok.com/@dihenhocungthachthao/video/7526382605126651144
[2025-07-14 11:08:02.493072] TTC navigation successful
[2025-07-14 11:08:02.493571] TTC calling __clickXmlJobs with comment: True
[2025-07-14 11:09:51.612804] TTC tried Escape key 3 times to close popups
[2025-07-14 11:09:51.631768] TTC detected popup by text: Giới thiệu các phím tắt
[2025-07-14 11:09:51.632267] TTC starting aggressive popup closing
[2025-07-14 11:10:18.297215] TTC force removed modals with JavaScript
[2025-07-14 11:10:18.811739] TTC popup still visible after all attempts
[2025-07-14 11:10:18.812244] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:10:18.827210] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 11:10:46.215212] TTC TikTok job operation, forcing minisize for popup avoidance
[2025-07-14 11:10:46.215697] TTC auto-enabled minisize with scale factor: 0.2
[2025-07-14 11:10:47.827939] TTC window sizing for TikTok job, forcing minisize
[2025-07-14 11:10:47.936731] TTC auto-minisize applied with zoom: 1.0
[2025-07-14 11:10:54.579669] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 11:10:54.876088] Request successful, status: 200
[2025-07-14 11:10:54.876587] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 11:10:55.025302] Request successful, status: 200
[2025-07-14 11:11:09.895790] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 11:11:10.685790] Request successful, status: 200
[2025-07-14 11:11:15.688808] TTC getJob called for type: comment
[2025-07-14 11:11:15.688808] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=oj5ltkesa08blhtkuq765upqu3; Path=/'}
[2025-07-14 11:11:15.689807] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 11:11:15.832380] Request successful, status: 200
[2025-07-14 11:11:15.832879] TTC comment response status: 200
[2025-07-14 11:11:15.833378] TTC comment response text: {"error":"Lấy nhiệm vụ sau 50 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":50}...
[2025-07-14 11:11:15.833880] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 11:11:15.833880] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 50 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 50}
