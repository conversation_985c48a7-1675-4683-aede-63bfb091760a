[2025-07-14 10:27:18.706796] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 10:27:19.001733] Request successful, status: 200
[2025-07-14 10:27:19.003712] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 10:27:19.146430] Request successful, status: 200
[2025-07-14 10:27:34.512664] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 10:27:35.011156] Request successful, status: 200
[2025-07-14 10:27:40.014569] TTC getJob called for type: comment
[2025-07-14 10:27:40.015067] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=soosscb6bio4ahbnal3ch1d093; Path=/'}
[2025-07-14 10:27:40.015565] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:27:40.148304] Request successful, status: 200
[2025-07-14 10:27:40.149303] TTC comment response status: 200
[2025-07-14 10:27:40.149303] TTC comment response text: {"error":"Lấy nhiệm vụ sau 21 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":21}...
[2025-07-14 10:27:40.149801] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 10:27:40.150301] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 21 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 21}
[2025-07-14 10:28:01.159619] TTC getJob called for type: comment
[2025-07-14 10:28:01.159619] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=soosscb6bio4ahbnal3ch1d093; Path=/'}
[2025-07-14 10:28:01.160634] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 10:28:01.320819] Request successful, status: 200
[2025-07-14 10:28:01.321325] TTC comment response status: 200
[2025-07-14 10:28:01.321325] TTC comment response text: [{"idpost":"7517184022275951892","link":"https:\/\/www.tiktok.com\/@thulaminhthu\/video\/7517184022275951892","nd":"[\"trai \\u0111\\u1eb9p th\\u00edch trai \\u0111\\u1eb9p \\ud83e\\udd2d\",\"oppaaaaaa\",\"idol \\u0111i \\u0111u idol g\\u1eb7p 1 idol \\u0111i \\u0111u idol \\ud83e\\udd21\",\"To\\u00e0n c\\u00e1c a \\u0111zai\",\"cute qu\\u00e1 troiiii oiiii\"]"},{"idpost":"7526504322369064199","link":"https:\/\/www.tiktok.com\/@nhisophia999\/photo\/7526504322369064199","nd":"[\"N\\u00e0y l\\u00e...
[2025-07-14 10:28:01.322319] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 10:28:01.322815] TTC comment - Jobs list detected, length: 36
[2025-07-14 10:28:01.370223] TTC starting jobs iteration: 36 jobs
[2025-07-14 10:28:01.370707] TTC comment settings: max=12345, cache=2
[2025-07-14 10:28:01.371720] TTC processing job 1: 7517184022275951892, https://www.tiktok.com/@thulaminhthu/video/7517184022275951892
[2025-07-14 10:28:01.372355] TTC comment content found: ["trai \u0111\u1eb9p th\u00edch trai \u0111\u1eb9p \ud83e\udd2d","oppaaaaaa","idol \u0111i \u0111u i...
[2025-07-14 10:28:01.373218] TTC navigating to: https://www.tiktok.com/@thulaminhthu/video/7517184022275951892
[2025-07-14 10:28:05.872403] TTC navigation successful
[2025-07-14 10:28:05.872917] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:28:21.661269] TTC tried Escape key 3 times to close popups
[2025-07-14 10:28:23.082477] TTC forced close keyboard shortcuts popup with multiple ESC
[2025-07-14 10:28:23.082994] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:28:23.103456] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:28:35.384890] TTC tried Escape key 3 times to close popups
[2025-07-14 10:28:36.532869] TTC comment input clicked with ActionChains
[2025-07-14 10:28:38.276472] TTC cleaned comment text: 'oppaaaaaa' (length: 9)
[2025-07-14 10:28:38.276972] TTC typing comment: oppaaaaaa...
[2025-07-14 10:28:39.882346] TTC typing method 1 result: expected='oppaaaaaa', actual='ppaaaaaa'
[2025-07-14 10:28:39.882346] TTC typing method 1 success: 8/9 chars
[2025-07-14 10:28:40.889385] TTC verify attempt 1: expected='oppaaaaaa...', actual='ppaaaaaa...'
[2025-07-14 10:28:41.421871] TTC comment input current value: 'ppaaaaaa...'
[2025-07-14 10:28:41.422370] TTC comment text verified in input
[2025-07-14 10:28:41.429357] TTC detected missing first character, fixing...
[2025-07-14 10:28:42.257743] TTC final verification passed: 'oppaaaaaa...'
[2025-07-14 10:28:43.282901] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:28:43.589594] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:28:45.646669] TTC comment not found in list (may still be processing)
[2025-07-14 10:28:45.647169] TTC comment posted successfully
[2025-07-14 10:28:45.648168] TTC __clickXmlJobs result: True
[2025-07-14 10:29:05.653208] TTC processing job 2: 7526504322369064199, https://www.tiktok.com/@nhisophia999/photo/7526504322369064199
[2025-07-14 10:29:05.654206] TTC comment content found: ["N\u00e0y l\u00e0 l\u1ec5 g\u00ec \u00e1 b\u1ea1n","V\u1ebd T\u1ebft \u0111oan ng\u1ecd y ch\u1ebf"...
[2025-07-14 10:29:05.655203] TTC navigating to: https://www.tiktok.com/@nhisophia999/photo/7526504322369064199
[2025-07-14 10:29:07.383979] TTC navigation successful
[2025-07-14 10:29:07.384477] TTC calling __clickXmlJobs with comment: True
[2025-07-14 10:29:23.278363] TTC tried Escape key 3 times to close popups
[2025-07-14 10:29:23.821248] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:29:23.842712] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 10:29:36.126460] TTC tried Escape key 3 times to close popups
[2025-07-14 10:29:37.323626] TTC comment input clicked with ActionChains
[2025-07-14 10:29:39.125448] TTC cleaned comment text: 'Check ib m bn ơi' (length: 16)
[2025-07-14 10:29:39.125947] TTC typing comment: Check ib m bn ơi...
[2025-07-14 10:29:40.770405] TTC typing method 1 result: expected='Check ib m bn ơi', actual='heck ib m bn ơi'
[2025-07-14 10:29:40.771408] TTC typing method 1 success: 15/16 chars
[2025-07-14 10:29:41.795421] TTC verify attempt 1: expected='Check ib m bn ơi...', actual='heck ib m bn ơi...'
[2025-07-14 10:29:42.326878] TTC comment input current value: 'heck ib m bn ơi...'
[2025-07-14 10:29:42.327377] TTC comment text verified in input
[2025-07-14 10:29:42.333884] TTC detected missing first character, fixing...
[2025-07-14 10:29:43.160285] TTC final verification passed: 'Check ib m bn ơi...'
[2025-07-14 10:29:44.187295] TTC found post button: [data-e2e="comment-post"]
[2025-07-14 10:29:44.510698] TTC post button clicked with method 1: [data-e2e="comment-post"]
[2025-07-14 10:29:46.551184] TTC comment verified in comment list
[2025-07-14 10:29:46.551677] TTC comment posted successfully
[2025-07-14 10:29:46.552181] TTC __clickXmlJobs result: True
[2025-07-14 10:29:46.552675] TTC comment cache reached: 2/2
