﻿[2025-07-14 04:49:28.521662] Attempting request to: https://tuongtaccheo.com/login.php
[2025-07-14 04:49:28.872174] Request successful, status: 200
[2025-07-14 04:49:28.873121] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 04:49:29.028321] Request successful, status: 200
[2025-07-14 04:49:43.687993] Attempting request to: https://tuongtaccheo.com/cauhinh/addtiktok.php?link=g.hoang.5n.2006&nickchay=g.hoang.5n.2006
[2025-07-14 04:49:44.590529] Request successful, status: 200
[2025-07-14 04:49:49.593292] TTC getJob called for type: comment
[2025-07-14 04:49:49.593292] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=2nan3jg0rmec1vo11da3mg0q73; Path=/'}
[2025-07-14 04:49:49.594279] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 04:49:49.725618] Request successful, status: 200
[2025-07-14 04:49:49.726116] TTC comment response status: 200
[2025-07-14 04:49:49.726116] TTC comment response text: {"error":"Lấy nhiệm vụ sau 26 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!","countdown":26}...
[2025-07-14 04:49:49.726615] TTC comment parsed JSON type: <class 'dict'>
[2025-07-14 04:49:49.727224] TTC comment - Error response detected: {'error': 'Lấy nhiệm vụ sau 26 giây, spam sẽ văng tài khoản, đi làm nhiệm vụ khác quay lại sau!', 'countdown': 26}
[2025-07-14 04:50:15.739359] TTC getJob called for type: comment
[2025-07-14 04:50:15.739359] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=2nan3jg0rmec1vo11da3mg0q73; Path=/'}
[2025-07-14 04:50:15.740356] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 04:50:15.897554] Request successful, status: 200
[2025-07-14 04:50:15.898052] TTC comment response status: 200
[2025-07-14 04:50:15.898052] TTC comment response text: [{"idpost":"7523226627875425544","link":"https:\/\/www.tiktok.com\/@ngoclan3815\/video\/7523226627875425544","nd":"[\"Y\\u00eau n\\u00f3\",\"T\\u1ea5t c\\u1ea3 trang ph\\u1ee5c c\\u1ee7a b\\u1ea1n \\u0111\\u1ec1u tuy\\u1ec7t \\u0111\\u1eb9p\",\"\\u0110i\\u1ec1u n\\u00e0y tr\\u00f4ng r\\u1ea5t t\\u1ed1t\",\"\\u0110i\\u1ec1u n\\u00e0y tr\\u00f4ng r\\u1ea5t t\\u1ed1t\",\"\\u2728\\u2728\\u2728\\u2728\\u2728\\u2728\",\"L\\u00e0m \\u01a1n h\\u00e3y ch\\u00fa \\u00fd \\u0111\\u1ebfn t\\u00f4i\",\"Angel...
[2025-07-14 04:50:15.899553] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 04:50:15.900049] TTC comment - Jobs list detected, length: 36
[2025-07-14 04:50:15.934483] TTC starting jobs iteration: 36 jobs
[2025-07-14 04:50:15.934984] TTC comment settings: max=2, cache=2
[2025-07-14 04:50:15.935483] TTC processing job 1: 7523226627875425544, https://www.tiktok.com/@ngoclan3815/video/7523226627875425544
[2025-07-14 04:50:15.935986] TTC comment content found: ["Y\u00eau n\u00f3","T\u1ea5t c\u1ea3 trang ph\u1ee5c c\u1ee7a b\u1ea1n \u0111\u1ec1u tuy\u1ec7t \u0...
[2025-07-14 04:50:15.940956] TTC navigating to: https://www.tiktok.com/@ngoclan3815/video/7523226627875425544
[2025-07-14 04:50:20.256776] TTC navigation successful
[2025-07-14 04:50:20.257614] TTC calling __clickXmlJobs with comment: True
[2025-07-14 04:50:31.550772] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:50:31.574729] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:50:40.698726] TTC comment input clicked successfully
[2025-07-14 04:50:42.548348] TTC typing comment: Tốt asf...
[2025-07-14 04:50:45.444213] TTC typing method 2 success: 7/7 chars
[2025-07-14 04:50:46.453095] TTC verify attempt 1: expected='Tốt asf...', actual='Tốt asf...'
[2025-07-14 04:50:46.995048] TTC comment input current value: 'Tốt asf...'
[2025-07-14 04:50:46.995564] TTC comment text verified in input
[2025-07-14 04:50:47.008039] TTC final verification passed: 'Tốt asf...'
[2025-07-14 04:50:48.119462] TTC post button clicked with JavaScript
[2025-07-14 04:50:50.189179] TTC comment not found in list (may still be processing)
[2025-07-14 04:50:50.189649] TTC comment posted with CSS: [data-e2e="comment-post"]
[2025-07-14 04:50:50.190147] TTC breaking selector loop - posted: True, typed: True
[2025-07-14 04:50:50.190668] TTC __clickXmlJobs result: True
[2025-07-14 04:50:50.191163] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 04:50:50.337385] Request successful, status: 200
[2025-07-14 04:50:53.339065] TTC processing job 2: 7526550871191080212, https://www.tiktok.com/@vyhareview/video/7526550871191080212
[2025-07-14 04:50:53.339546] TTC comment content found: ["Ngon th\u1ebf n\u00e0y kh\u00f4ng \u0103n th\u00ec ph\u00ed em nh\u1ec9","Ult g\u00ec ch\u1ee9 kem...
[2025-07-14 04:50:53.340044] TTC navigating to: https://www.tiktok.com/@vyhareview/video/7526550871191080212
[2025-07-14 04:50:57.721562] TTC navigation successful
[2025-07-14 04:50:57.722072] TTC calling __clickXmlJobs with comment: True
[2025-07-14 04:51:08.167765] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:51:08.192721] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:51:17.388220] TTC comment input clicked successfully
[2025-07-14 04:51:19.180878] TTC typing comment: tìm được kem 35 chuẩn ăn cho ngon nhá...
[2025-07-14 04:51:20.758070] TTC typing method 1 success: 36/37 chars
[2025-07-14 04:51:21.767311] TTC verify attempt 1: expected='tìm được kem 35 chuẩn ăn cho n...', actual='ìm được kem 35 chuẩn ăn cho ng...'
[2025-07-14 04:51:22.314573] TTC comment input current value: 'ìm được kem 35 chuẩn ăn cho ngon nhá...'
[2025-07-14 04:51:22.315068] TTC comment text verified in input
[2025-07-14 04:51:22.324050] TTC detected missing first character, fixing...
[2025-07-14 04:51:23.165954] TTC final verification passed: 'tìm được kem 35 chuẩn ăn cho ngon nhá...'
[2025-07-14 04:51:24.287331] TTC post button clicked with JavaScript
[2025-07-14 04:51:26.338870] TTC comment verified in comment list
[2025-07-14 04:51:26.339387] TTC comment posted with CSS: [data-e2e="comment-post"]
[2025-07-14 04:51:26.340384] TTC breaking selector loop - posted: True, typed: True
[2025-07-14 04:51:26.340901] TTC __clickXmlJobs result: True
[2025-07-14 04:51:26.341386] TTC comment cache reached: 2/2
[2025-07-14 04:51:46.350079] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/nhantien.php
[2025-07-14 04:51:46.488940] Request successful, status: 200
[2025-07-14 04:51:46.489922] TTC getXuJob response: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 04:51:46.490919] TTC getXuJob failed: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 04:51:46.491417] TTC comment xu delay: 2 seconds
[2025-07-14 04:51:48.492076] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 04:51:48.644802] Request successful, status: 200
[2025-07-14 04:51:51.647750] TTC getJob called for type: comment
[2025-07-14 04:51:51.647750] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=2nan3jg0rmec1vo11da3mg0q73; Path=/'}
[2025-07-14 04:51:51.648251] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 04:51:51.825917] Request successful, status: 200
[2025-07-14 04:51:51.826414] TTC comment response status: 200
[2025-07-14 04:51:51.826414] TTC comment response text: [{"idpost":"7526489343817616647","link":"https:\/\/www.tiktok.com\/@hoanggvu.1234\/photo\/7526489343817616647","nd":"[\"Cho em theo anh d\\u1edbi\",\"Acc c\\u0169 \\u0111\\u00e2u a\"]"},{"idpost":"7526508273835576594","link":"https:\/\/www.tiktok.com\/@hangtiktokshop2_90\/video\/7526508273835576594","nd":"[\"Em mu\\u1ed1n h\\u1ecdc h\\u1ecfi ch\\u1ecb, ch\\u1ec9 em nh\\u00e9\",\"Nh\\u00ecn x\\u1ecbn qu\\u00e1, ch\\u1ecb share b\\u00ed k\\u00edp \\u0111i\",\"\\u01af\\u1edbc g\\u00ec ra th\\u1eadt...
[2025-07-14 04:51:51.827909] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 04:51:51.827909] TTC comment - Jobs list detected, length: 36
[2025-07-14 04:51:51.859333] TTC starting jobs iteration: 36 jobs
[2025-07-14 04:51:51.859833] TTC comment settings: max=2, cache=2
[2025-07-14 04:51:51.860350] TTC processing job 1: 7526489343817616647, https://www.tiktok.com/@hoanggvu.1234/photo/7526489343817616647
[2025-07-14 04:51:51.860832] TTC comment content found: ["Cho em theo anh d\u1edbi","Acc c\u0169 \u0111\u00e2u a"]...
[2025-07-14 04:51:51.867318] TTC navigating to: https://www.tiktok.com/@hoanggvu.1234/photo/7526489343817616647
[2025-07-14 04:51:53.405975] TTC navigation successful
[2025-07-14 04:51:53.406474] TTC calling __clickXmlJobs with comment: True
[2025-07-14 04:52:04.176542] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:52:04.200001] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:52:13.246108] TTC comment input clicked successfully
[2025-07-14 04:52:15.044959] TTC typing comment: Cho em theo anh dới...
[2025-07-14 04:52:16.150607] TTC typing method 1 success: 18/19 chars
[2025-07-14 04:52:17.160121] TTC verify attempt 1: expected='Cho em theo anh dới...', actual='ho em theo anh dới...'
[2025-07-14 04:52:17.705005] TTC comment input current value: 'ho em theo anh dới...'
[2025-07-14 04:52:17.705502] TTC comment text verified in input
[2025-07-14 04:52:17.714486] TTC detected missing first character, fixing...
[2025-07-14 04:52:18.569311] TTC final verification passed: 'Cho em theo anh dới...'
[2025-07-14 04:52:19.667218] TTC post button clicked with JavaScript
[2025-07-14 04:52:21.736107] TTC comment not found in list (may still be processing)
[2025-07-14 04:52:21.736607] TTC comment posted with CSS: [data-e2e="comment-post"]
[2025-07-14 04:52:21.737589] TTC breaking selector loop - posted: True, typed: True
[2025-07-14 04:52:21.738105] TTC __clickXmlJobs result: True
[2025-07-14 04:52:21.738603] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 04:52:21.912258] Request successful, status: 200
[2025-07-14 04:52:24.914355] TTC processing job 2: 7526508273835576594, https://www.tiktok.com/@hangtiktokshop2_90/video/7526508273835576594
[2025-07-14 04:52:24.914853] TTC comment content found: ["Em mu\u1ed1n h\u1ecdc h\u1ecfi ch\u1ecb, ch\u1ec9 em nh\u00e9","Nh\u00ecn x\u1ecbn qu\u00e1, ch\u1...
[2025-07-14 04:52:24.915356] TTC navigating to: https://www.tiktok.com/@hangtiktokshop2_90/video/7526508273835576594
[2025-07-14 04:52:29.701279] TTC navigation successful
[2025-07-14 04:52:29.701777] TTC calling __clickXmlJobs with comment: True
[2025-07-14 04:52:39.776557] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:52:39.800516] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:52:48.867075] TTC comment input clicked successfully
[2025-07-14 04:52:50.579333] TTC typing comment: Chị check tin nhắn em gửi đi ạ, chờ lâu rồi...
[2025-07-14 04:52:52.384772] TTC typing method 1 success: 42/43 chars
[2025-07-14 04:52:53.394596] TTC verify attempt 1: expected='Chị check tin nhắn em gửi đi ạ...', actual='hị check tin nhắn em gửi đi ạ,...'
[2025-07-14 04:52:53.946039] TTC comment input current value: 'hị check tin nhắn em gửi đi ạ, chờ lâu rồi...'
[2025-07-14 04:52:53.946538] TTC comment text verified in input
[2025-07-14 04:52:53.954024] TTC detected missing first character, fixing...
[2025-07-14 04:52:54.802420] TTC final verification passed: 'Chị check tin nhắn em gửi đi ạ, chờ lâu rồi...'
[2025-07-14 04:52:55.917312] TTC post button clicked with JavaScript
[2025-07-14 04:52:57.984099] TTC comment verified in comment list
[2025-07-14 04:52:57.985079] TTC comment posted with CSS: [data-e2e="comment-post"]
[2025-07-14 04:52:57.985596] TTC breaking selector loop - posted: True, typed: True
[2025-07-14 04:52:57.986114] TTC __clickXmlJobs result: True
[2025-07-14 04:52:57.986595] TTC comment cache reached: 2/2
[2025-07-14 04:53:17.995175] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/nhantien.php
[2025-07-14 04:53:18.132914] Request successful, status: 200
[2025-07-14 04:53:18.133897] TTC getXuJob response: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 04:53:18.134413] TTC getXuJob failed: {'status': 'error', 'mess': 'Video này nhận lỗi quá nhiều, hãy làm các nhiệm vụ khác'}
[2025-07-14 04:53:18.135394] TTC comment xu delay: 2 seconds
[2025-07-14 04:53:20.136118] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 04:53:20.275259] Request successful, status: 200
[2025-07-14 04:53:23.277830] TTC getJob called for type: comment
[2025-07-14 04:53:23.277830] TTC headers: {'Content-type': 'application/x-www-form-urlencoded', 'x-requested-with': 'XMLHttpRequest', 'Cookie': 'PHPSESSID=2nan3jg0rmec1vo11da3mg0q73; Path=/'}
[2025-07-14 04:53:23.278326] Attempting request to: https://tuongtaccheo.com/tiktok/kiemtien/cmtcheo/getpost.php
[2025-07-14 04:53:23.437026] Request successful, status: 200
[2025-07-14 04:53:23.437527] TTC comment response status: 200
[2025-07-14 04:53:23.437527] TTC comment response text: [{"idpost":"7525068798617980178","link":"https:\/\/www.tiktok.com\/@limhlinh02\/video\/7525068798617980178","nd":"[\"Th\\u00f9ng bao nhi\\u00eau g\\u00f3i v\\u1eady \\u1ea1\",\"Gi\\u00e1 ph\\u00f9 h\\u1ee3p\",\"\\u0110\\u00e3 \\u0111\\u1eb7t\",\".\",\"Kh\\u0103n d\\u00f9ng th\\u00edch l\\u1eafm\"]"},{"idpost":"7526210930955259144","link":"https:\/\/www.tiktok.com\/@b_ply\/video\/7526210930955259144","nd":"[\"\",\"\",\"\",\"H\\u01a1i s\\u01b0\\u01a1ng m\\u1ecbn c\\u1ef1c \\ud83d\\ude1a, kh\\u00f4...
[2025-07-14 04:53:23.438523] TTC comment parsed JSON type: <class 'list'>
[2025-07-14 04:53:23.439025] TTC comment - Jobs list detected, length: 36
[2025-07-14 04:53:23.479930] TTC starting jobs iteration: 36 jobs
[2025-07-14 04:53:23.480449] TTC comment settings: max=12345, cache=2
[2025-07-14 04:53:23.480943] TTC processing job 1: 7525068798617980178, https://www.tiktok.com/@limhlinh02/video/7525068798617980178
[2025-07-14 04:53:23.481452] TTC comment content found: ["Th\u00f9ng bao nhi\u00eau g\u00f3i v\u1eady \u1ea1","Gi\u00e1 ph\u00f9 h\u1ee3p","\u0110\u00e3 \u0...
[2025-07-14 04:53:23.493404] TTC navigating to: https://www.tiktok.com/@limhlinh02/video/7525068798617980178
[2025-07-14 04:53:25.287529] TTC navigation successful
[2025-07-14 04:53:25.288026] TTC calling __clickXmlJobs with comment: True
[2025-07-14 04:53:37.575760] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:53:37.600173] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:53:46.781268] TTC comment input clicked successfully
[2025-07-14 04:53:48.579370] TTC typing comment: Đã đặt...
[2025-07-14 04:53:51.484879] TTC typing method 2 success: 6/6 chars
[2025-07-14 04:53:52.496452] TTC verify attempt 1: expected='Đã đặt...', actual='Đã đặt...'
[2025-07-14 04:53:53.023952] TTC comment input current value: 'Đã đặt...'
[2025-07-14 04:53:53.024453] TTC comment text verified in input
[2025-07-14 04:53:53.033436] TTC final verification passed: 'Đã đặt...'
[2025-07-14 04:53:54.142052] TTC post button clicked with JavaScript
[2025-07-14 04:53:56.206652] TTC comment verified in comment list
[2025-07-14 04:53:56.207149] TTC comment posted with CSS: [data-e2e="comment-post"]
[2025-07-14 04:53:56.207651] TTC breaking selector loop - posted: True, typed: True
[2025-07-14 04:53:56.208165] TTC __clickXmlJobs result: True
[2025-07-14 04:53:56.208664] Attempting request to: https://tuongtaccheo.com/home.php
[2025-07-14 04:53:56.355390] Request successful, status: 200
[2025-07-14 04:53:59.357694] TTC processing job 2: 7526210930955259144, https://www.tiktok.com/@b_ply/video/7526210930955259144
[2025-07-14 04:53:59.358693] TTC comment content found: ["","","","H\u01a1i s\u01b0\u01a1ng m\u1ecbn c\u1ef1c \ud83d\ude1a, kh\u00f4ng ki\u1ec3u phun ra v\u...
[2025-07-14 04:53:59.360189] TTC navigating to: https://www.tiktok.com/@b_ply/video/7526210930955259144
[2025-07-14 04:54:04.647700] TTC navigation successful
[2025-07-14 04:54:04.648199] TTC calling __clickXmlJobs with comment: True
[2025-07-14 04:54:16.110535] TTC trying selector: div[data-e2e="comment-input"] div[contenteditable="true"]
[2025-07-14 04:54:16.118520] TTC found comment input with selector: div[data-e2e="comment-input"] div[contenteditable="true"]
